@echo off
REM =============================================================================
REM Auto Setup Script for RNN Chatbot (Windows Version)
REM =============================================================================

setlocal enabledelayedexpansion

echo.
echo ========================================
echo 🤖 RNN Chatbot Auto Setup (Windows)
echo ========================================
echo.

REM Check Python installation
echo [INFO] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH!
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

REM Get Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [SUCCESS] Found Python version: %PYTHON_VERSION%

REM Create virtual environment
echo.
echo [INFO] Creating virtual environment...
if exist "rnn_chatbot_env" (
    echo [INFO] Removing existing environment...
    rmdir /s /q "rnn_chatbot_env"
)

python -m venv rnn_chatbot_env
if errorlevel 1 (
    echo [ERROR] Failed to create virtual environment!
    pause
    exit /b 1
)

REM Activate virtual environment
echo [INFO] Activating virtual environment...
call rnn_chatbot_env\Scripts\activate.bat

REM Upgrade pip
echo [INFO] Upgrading pip...
python -m pip install --upgrade pip setuptools wheel

REM Install dependencies
echo.
echo [INFO] Installing Python dependencies...
echo This may take several minutes...

pip install numpy>=1.21.0
if errorlevel 1 echo [WARNING] Failed to install numpy

pip install tensorflow>=2.12.0
if errorlevel 1 (
    echo [WARNING] Failed to install tensorflow, trying CPU version...
    pip install tensorflow-cpu
)

pip install scikit-learn>=1.0.0
if errorlevel 1 echo [WARNING] Failed to install scikit-learn

pip install matplotlib>=3.5.0
if errorlevel 1 echo [WARNING] Failed to install matplotlib

pip install seaborn>=0.11.0
if errorlevel 1 echo [WARNING] Failed to install seaborn

pip install pandas>=1.3.0
if errorlevel 1 echo [WARNING] Failed to install pandas

pip install tqdm>=4.64.0
if errorlevel 1 echo [WARNING] Failed to install tqdm

REM Check data files
echo.
echo [INFO] Checking data files...
set DATA_COUNT=0

if exist "final_1m_simsimi.json" (
    echo [SUCCESS] Found: final_1m_simsimi.json
    set /a DATA_COUNT+=1
)
if exist "cleaned_natural_simsimi.json" (
    echo [SUCCESS] Found: cleaned_natural_simsimi.json
    set /a DATA_COUNT+=1
)
if exist "100k_asks_500k_answers.json" (
    echo [SUCCESS] Found: 100k_asks_500k_answers.json
    set /a DATA_COUNT+=1
)

if %DATA_COUNT% equ 0 (
    echo [ERROR] No data files found!
    echo Please ensure conversation data files are in the current directory.
    pause
    exit /b 1
)

echo [SUCCESS] Found %DATA_COUNT% data files for training

REM Train the model
echo.
echo [INFO] Starting RNN model training...
echo This may take 30-60 minutes depending on your hardware...
echo.

python train_chatbot.py
if errorlevel 1 (
    echo [ERROR] Model training failed!
    pause
    exit /b 1
)

REM Test the model
echo.
echo [INFO] Testing trained model...

REM Create quick test
echo import sys > quick_test.py
echo sys.path.append('.') >> quick_test.py
echo try: >> quick_test.py
echo     from rnn_chatbot import ConversationalAI >> quick_test.py
echo     ai = ConversationalAI(data_files=[], vocab_size=20000, max_length=50, embedding_dim=256, hidden_dim=512) >> quick_test.py
echo     ai.load_model("trained_chatbot") >> quick_test.py
echo     response = ai.generate_response("hello", temperature=0.7) >> quick_test.py
echo     print("Test response:", response) >> quick_test.py
echo     print("✅ Model test passed!") >> quick_test.py
echo except Exception as e: >> quick_test.py
echo     print("❌ Model test failed:", e) >> quick_test.py

python quick_test.py
del quick_test.py

REM Create startup batch files
echo.
echo [INFO] Creating startup scripts...

REM Chat startup script
echo @echo off > start_chat.bat
echo call rnn_chatbot_env\Scripts\activate.bat >> start_chat.bat
echo python chat_with_bot.py >> start_chat.bat
echo pause >> start_chat.bat

REM Evaluation script
echo @echo off > run_evaluation.bat
echo call rnn_chatbot_env\Scripts\activate.bat >> run_evaluation.bat
echo python evaluate_chatbot.py >> run_evaluation.bat
echo pause >> run_evaluation.bat

REM Retrain script
echo @echo off > retrain_model.bat
echo call rnn_chatbot_env\Scripts\activate.bat >> retrain_model.bat
echo python train_chatbot.py >> retrain_model.bat
echo pause >> retrain_model.bat

echo.
echo ========================================
echo 🎉 Setup Complete!
echo ========================================
echo.
echo [SUCCESS] RNN Chatbot setup completed successfully!
echo.
echo Available commands:
echo   🗣️  Start chatting:     start_chat.bat
echo   📊 Run evaluation:     run_evaluation.bat
echo   🔄 Retrain model:      retrain_model.bat
echo.
echo Manual commands:
echo   Activate environment:  rnn_chatbot_env\Scripts\activate.bat
echo   Train model:          python train_chatbot.py
echo   Start chat:           python chat_with_bot.py
echo   Evaluate model:       python evaluate_chatbot.py
echo.
echo 🚀 Your RNN Chatbot is ready to use!
echo.
pause