/*! For license information please see 5090.404be96d8a6eae1e719a.js.LICENSE.txt */
"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[5090],{67002:(t,n,e)=>{e.d(n,{t:()=>o});const o={horizontal:"horizontal",vertical:"vertical"}},74291:(t,n,e)=>{e.d(n,{Ac:()=>Dt,De:()=>xt,F9:()=>kt,FM:()=>At,HX:()=>bt,I5:()=>Pt,Is:()=>Mt,J9:()=>Ct,Mm:()=>Et,R9:()=>Tt,Tg:()=>Rt,bb:()=>St,f_:()=>Nt,gG:()=>It,kT:()=>yt,oK:()=>Lt});var o;(function(t){t[t["alt"]=18]="alt";t[t["arrowDown"]=40]="arrowDown";t[t["arrowLeft"]=37]="arrowLeft";t[t["arrowRight"]=39]="arrowRight";t[t["arrowUp"]=38]="arrowUp";t[t["back"]=8]="back";t[t["backSlash"]=220]="backSlash";t[t["break"]=19]="break";t[t["capsLock"]=20]="capsLock";t[t["closeBracket"]=221]="closeBracket";t[t["colon"]=186]="colon";t[t["colon2"]=59]="colon2";t[t["comma"]=188]="comma";t[t["ctrl"]=17]="ctrl";t[t["delete"]=46]="delete";t[t["end"]=35]="end";t[t["enter"]=13]="enter";t[t["equals"]=187]="equals";t[t["equals2"]=61]="equals2";t[t["equals3"]=107]="equals3";t[t["escape"]=27]="escape";t[t["forwardSlash"]=191]="forwardSlash";t[t["function1"]=112]="function1";t[t["function10"]=121]="function10";t[t["function11"]=122]="function11";t[t["function12"]=123]="function12";t[t["function2"]=113]="function2";t[t["function3"]=114]="function3";t[t["function4"]=115]="function4";t[t["function5"]=116]="function5";t[t["function6"]=117]="function6";t[t["function7"]=118]="function7";t[t["function8"]=119]="function8";t[t["function9"]=120]="function9";t[t["home"]=36]="home";t[t["insert"]=45]="insert";t[t["menu"]=93]="menu";t[t["minus"]=189]="minus";t[t["minus2"]=109]="minus2";t[t["numLock"]=144]="numLock";t[t["numPad0"]=96]="numPad0";t[t["numPad1"]=97]="numPad1";t[t["numPad2"]=98]="numPad2";t[t["numPad3"]=99]="numPad3";t[t["numPad4"]=100]="numPad4";t[t["numPad5"]=101]="numPad5";t[t["numPad6"]=102]="numPad6";t[t["numPad7"]=103]="numPad7";t[t["numPad8"]=104]="numPad8";t[t["numPad9"]=105]="numPad9";t[t["numPadDivide"]=111]="numPadDivide";t[t["numPadDot"]=110]="numPadDot";t[t["numPadMinus"]=109]="numPadMinus";t[t["numPadMultiply"]=106]="numPadMultiply";t[t["numPadPlus"]=107]="numPadPlus";t[t["openBracket"]=219]="openBracket";t[t["pageDown"]=34]="pageDown";t[t["pageUp"]=33]="pageUp";t[t["period"]=190]="period";t[t["print"]=44]="print";t[t["quote"]=222]="quote";t[t["scrollLock"]=145]="scrollLock";t[t["shift"]=16]="shift";t[t["space"]=32]="space";t[t["tab"]=9]="tab";t[t["tilde"]=192]="tilde";t[t["windowsLeft"]=91]="windowsLeft";t[t["windowsOpera"]=219]="windowsOpera";t[t["windowsRight"]=92]="windowsRight"})(o||(o={}));const r=18;const a=40;const c=37;const i=39;const s=38;const u=8;const l=220;const d=19;const f=20;const p=221;const m=186;const h=59;const v=188;const w=17;const g=46;const b=35;const y=13;const S=187;const P=61;const E=107;const k=27;const R=191;const A=112;const D=121;const N=122;const L=123;const I=113;const C=114;const T=115;const x=116;const M=117;const U=118;const O=119;const q=120;const B=36;const F=45;const _=93;const j=189;const z=109;const G=144;const H=96;const V=97;const X=98;const $=99;const J=100;const K=101;const Y=102;const Q=103;const W=104;const Z=105;const tt=111;const nt=110;const et=109;const ot=106;const rt=107;const at=219;const ct=34;const it=33;const st=190;const ut=44;const lt=222;const dt=145;const ft=16;const pt=32;const mt=9;const ht=192;const vt=91;const wt=219;const gt=92;const bt="ArrowDown";const yt="ArrowLeft";const St="ArrowRight";const Pt="ArrowUp";const Et="Enter";const kt="Escape";const Rt="Home";const At="End";const Dt="F2";const Nt="PageDown";const Lt="PageUp";const It=" ";const Ct="Tab";const Tt="Backspace";const xt="Delete";const Mt={ArrowDown:bt,ArrowLeft:yt,ArrowRight:St,ArrowUp:Pt}},30086:(t,n,e)=>{e.d(n,{O:()=>o});var o;(function(t){t["ltr"]="ltr";t["rtl"]="rtl"})(o||(o={}))},83021:(t,n,e)=>{e.d(n,{AB:()=>r,Vf:()=>o,r4:()=>a});function o(t,n,e){if(e<t){return n}else if(e>n){return t}return e}function r(t,n,e){return Math.min(Math.max(e,t),n)}function a(t,n,e=0){[n,e]=[n,e].sort(((t,n)=>t-n));return n<=t&&t<e}},49054:(t,n,e)=>{e.d(n,{AO:()=>N,tp:()=>I});var o=["input","select","textarea","a[href]","button","[tabindex]:not(slot)","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])',"details>summary:first-of-type","details"];var r=o.join(",");var a=typeof Element==="undefined";var c=a?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector;var i=!a&&Element.prototype.getRootNode?function(t){return t.getRootNode()}:function(t){return t.ownerDocument};var s=function t(n,e,o){var a=Array.prototype.slice.apply(n.querySelectorAll(r));if(e&&c.call(n,r)){a.unshift(n)}a=a.filter(o);return a};var u=function t(n,e,o){var a=[];var i=Array.from(n);while(i.length){var s=i.shift();if(s.tagName==="SLOT"){var u=s.assignedElements();var l=u.length?u:s.children;var d=t(l,true,o);if(o.flatten){a.push.apply(a,d)}else{a.push({scope:s,candidates:d})}}else{var f=c.call(s,r);if(f&&o.filter(s)&&(e||!n.includes(s))){a.push(s)}var p=s.shadowRoot||typeof o.getShadowRoot==="function"&&o.getShadowRoot(s);var m=!o.shadowRootFilter||o.shadowRootFilter(s);if(p&&m){var h=t(p===true?s.children:p.children,true,o);if(o.flatten){a.push.apply(a,h)}else{a.push({scope:s,candidates:h})}}else{i.unshift.apply(i,s.children)}}}return a};var l=function t(n,e){if(n.tabIndex<0){if((e||/^(AUDIO|VIDEO|DETAILS)$/.test(n.tagName)||n.isContentEditable)&&isNaN(parseInt(n.getAttribute("tabindex"),10))){return 0}}return n.tabIndex};var d=function t(n,e){return n.tabIndex===e.tabIndex?n.documentOrder-e.documentOrder:n.tabIndex-e.tabIndex};var f=function t(n){return n.tagName==="INPUT"};var p=function t(n){return f(n)&&n.type==="hidden"};var m=function t(n){var e=n.tagName==="DETAILS"&&Array.prototype.slice.apply(n.children).some((function(t){return t.tagName==="SUMMARY"}));return e};var h=function t(n,e){for(var o=0;o<n.length;o++){if(n[o].checked&&n[o].form===e){return n[o]}}};var v=function t(n){if(!n.name){return true}var e=n.form||i(n);var o=function t(n){return e.querySelectorAll('input[type="radio"][name="'+n+'"]')};var r;if(typeof window!=="undefined"&&typeof window.CSS!=="undefined"&&typeof window.CSS.escape==="function"){r=o(window.CSS.escape(n.name))}else{try{r=o(n.name)}catch(c){console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",c.message);return false}}var a=h(r,n.form);return!a||a===n};var w=function t(n){return f(n)&&n.type==="radio"};var g=function t(n){return w(n)&&!v(n)};var b=function t(n){var e=n.getBoundingClientRect(),o=e.width,r=e.height;return o===0&&r===0};var y=function t(n,e){var o=e.displayCheck,r=e.getShadowRoot;if(getComputedStyle(n).visibility==="hidden"){return true}var a=c.call(n,"details>summary:first-of-type");var s=a?n.parentElement:n;if(c.call(s,"details:not([open]) *")){return true}var u=i(n).host;var l=(u===null||u===void 0?void 0:u.ownerDocument.contains(u))||n.ownerDocument.contains(n);if(!o||o==="full"){if(typeof r==="function"){var d=n;while(n){var f=n.parentElement;var p=i(n);if(f&&!f.shadowRoot&&r(f)===true){return b(n)}else if(n.assignedSlot){n=n.assignedSlot}else if(!f&&p!==n.ownerDocument){n=p.host}else{n=f}}n=d}if(l){return!n.getClientRects().length}}else if(o==="non-zero-area"){return b(n)}return false};var S=function t(n){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(n.tagName)){var e=n.parentElement;while(e){if(e.tagName==="FIELDSET"&&e.disabled){for(var o=0;o<e.children.length;o++){var r=e.children.item(o);if(r.tagName==="LEGEND"){return c.call(e,"fieldset[disabled] *")?true:!r.contains(n)}}return true}e=e.parentElement}}return false};var P=function t(n,e){if(e.disabled||p(e)||y(e,n)||m(e)||S(e)){return false}return true};var E=function t(n,e){if(g(e)||l(e)<0||!P(n,e)){return false}return true};var k=function t(n){var e=parseInt(n.getAttribute("tabindex"),10);if(isNaN(e)||e>=0){return true}return false};var R=function t(n){var e=[];var o=[];n.forEach((function(n,r){var a=!!n.scope;var c=a?n.scope:n;var i=l(c,a);var s=a?t(n.candidates):c;if(i===0){a?e.push.apply(e,s):e.push(c)}else{o.push({documentOrder:r,tabIndex:i,item:n,isScope:a,content:s})}}));return o.sort(d).reduce((function(t,n){n.isScope?t.push.apply(t,n.content):t.push(n.content);return t}),[]).concat(e)};var A=function t(n,e){e=e||{};var o;if(e.getShadowRoot){o=u([n],e.includeContainer,{filter:E.bind(null,e),flatten:false,getShadowRoot:e.getShadowRoot,shadowRootFilter:k})}else{o=s(n,e.includeContainer,E.bind(null,e))}return R(o)};var D=function t(n,e){e=e||{};var o;if(e.getShadowRoot){o=u([n],e.includeContainer,{filter:P.bind(null,e),flatten:true,getShadowRoot:e.getShadowRoot})}else{o=s(n,e.includeContainer,P.bind(null,e))}return o};var N=function t(n,e){e=e||{};if(!n){throw new Error("No node provided")}if(c.call(n,r)===false){return false}return E(e,n)};var L=o.concat("iframe").join(",");var I=function t(n,e){e=e||{};if(!n){throw new Error("No node provided")}if(c.call(n,L)===false){return false}return P(e,n)}}}]);