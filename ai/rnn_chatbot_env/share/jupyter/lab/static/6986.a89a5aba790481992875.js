"use strict";(self["webpackChunk_jupyterlab_application_top"]=self["webpackChunk_jupyterlab_application_top"]||[]).push([[6986],{21148:(e,a,r)=>{r.d(a,{K:()=>t});var l=Object.defineProperty;var t=(e,a)=>l(e,"name",{value:a,configurable:true})},96986:(e,a,r)=>{r.r(a);r.d(a,{default:()=>i});var l=r(21148);var t=(0,l.K)((async()=>await Promise.all([r.e(8606),r.e(2601),r.e(6439)]).then(r.bind(r,16439))),"loader");var p=["elk.stress","elk.force","elk.mrtree","elk.sporeOverlap"];var o=[{name:"elk",loader:t,algorithm:"elk.layered"},...p.map((e=>({name:e,loader:t,algorithm:e})))];var i=o}}]);