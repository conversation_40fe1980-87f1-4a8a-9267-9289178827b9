{"name": "@jupyterlab/console-extension", "version": "4.4.5", "description": "JupyterLab - Code Console Extension", "homepage": "https://github.com/jupyterlab/jupyterlab", "bugs": {"url": "https://github.com/jupyterlab/jupyterlab/issues"}, "repository": {"type": "git", "url": "https://github.com/jupyterlab/jupyterlab.git"}, "license": "BSD-3-<PERSON><PERSON>", "author": "Project Jupyter", "sideEffects": ["style/**/*.css", "style/index.js"], "main": "lib/index.js", "types": "lib/index.d.ts", "style": "style/index.css", "directories": {"lib": "lib/"}, "files": ["lib/*.d.ts", "lib/*.js.map", "lib/*.js", "schema/*.json", "style/**/*.css", "style/index.js", "src/**/*.{ts,tsx}"], "scripts": {"build": "tsc -b", "clean": "rimraf lib && rimraf tsconfig.tsbuildinfo", "watch": "tsc -b --watch"}, "dependencies": {"@jupyterlab/application": "^4.4.5", "@jupyterlab/apputils": "^4.5.5", "@jupyterlab/codeeditor": "^4.4.5", "@jupyterlab/completer": "^4.4.5", "@jupyterlab/console": "^4.4.5", "@jupyterlab/filebrowser": "^4.4.5", "@jupyterlab/launcher": "^4.4.5", "@jupyterlab/mainmenu": "^4.4.5", "@jupyterlab/rendermime": "^4.4.5", "@jupyterlab/settingregistry": "^4.4.5", "@jupyterlab/translation": "^4.4.5", "@jupyterlab/ui-components": "^4.4.5", "@lumino/algorithm": "^2.0.3", "@lumino/coreutils": "^2.2.1", "@lumino/disposable": "^2.1.4", "@lumino/properties": "^2.0.3", "@lumino/widgets": "^2.7.1"}, "devDependencies": {"rimraf": "~5.0.5", "typescript": "~5.5.4"}, "publishConfig": {"access": "public"}, "jupyterlab": {"extension": true, "schemaDir": "schema"}, "styleModule": "style/index.js"}