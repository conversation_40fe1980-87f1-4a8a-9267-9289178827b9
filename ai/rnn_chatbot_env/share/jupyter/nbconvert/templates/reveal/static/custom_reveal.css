/* Overrides of notebook CSS for static HTML export */
.reveal {
  font-size: 160%;
}
.reveal table {
  font-size: var(--jp-ui-font-size1);
}
.reveal pre {
  width: inherit;
  padding: 0.4em;
  margin: 0px;
  font-family: monospace, sans-serif;
  font-size: 80%;
  box-shadow: 0px 0px 0px rgba(0, 0, 0, 0);
}
.reveal pre code {
  padding: 0px;
}
.reveal section img {
  border: 0px solid black;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0);
}
.reveal .slides {
  text-align: left;
}
.reveal.fade {
  opacity: 1;
}
.reveal .progress {
  position: static;
}

div.jp-InputArea-editor {
  padding: 0.06em;
}

div.code_cell {
  background-color: transparent;
}

div.output_area pre {
  font-family: monospace, sans-serif;
  font-size: 80%;
}

div.jp-OutputPrompt {
  /* 5px right shift to account for margin in parent container */
  margin: 5px 5px 0 0;
}

.reveal div.highlight {
  margin: 0;
}

.reveal div.highlight > pre {
  margin: 0;
  width: 100%;
  font-size: var(--jp-code-font-size);
}

.reveal div.jp-OutputArea-output > pre {
  margin: 0;
  width: 90%;
  font-size: var(--jp-code-font-size);
  box-shadow: none;
}

main {
  height: 100%;
}

/* Reveal navigation controls */

.reveal .controls .navigate-left,
.reveal .controls .navigate-left.enabled {
  border-right-color: #727272;
}
.reveal .controls .navigate-left.enabled:hover,
.reveal .controls .navigate-left.enabled.enabled:hover {
  border-right-color: #dfdfdf;
}
.reveal .controls .navigate-right,
.reveal .controls .navigate-right.enabled {
  border-left-color: #727272;
}
.reveal .controls .navigate-right.enabled:hover,
.reveal .controls .navigate-right.enabled.enabled:hover {
  border-left-color: #dfdfdf;
}
.reveal .controls .navigate-up,
.reveal .controls .navigate-up.enabled {
  border-bottom-color: #727272;
}
.reveal .controls .navigate-up.enabled:hover,
.reveal .controls .navigate-up.enabled.enabled:hover {
  border-bottom-color: #dfdfdf;
}
.reveal .controls .navigate-down,
.reveal .controls .navigate-down.enabled {
  border-top-color: #727272;
}
.reveal .controls .navigate-down.enabled:hover,
.reveal .controls .navigate-down.enabled.enabled:hover {
  border-top-color: #dfdfdf;
}
.reveal .progress span {
  background: #727272;
}

/* Scrollbars */

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar * {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background: #727272 !important;
}
