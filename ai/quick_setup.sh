#!/bin/bash

# =============================================================================
# Quick Setup Script for RNN Chatbot (Simplified Version)
# =============================================================================

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🤖 Quick RNN Chatbot Setup${NC}"
echo "=========================="

# Check Python
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}Python3 not found! Installing...${NC}"
    sudo apt-get update && sudo apt-get install -y python3 python3-pip python3-venv
fi

# Create virtual environment
echo -e "${BLUE}Setting up environment...${NC}"
python3 -m venv rnn_env
source rnn_env/bin/activate

# Install dependencies
echo -e "${BLUE}Installing dependencies...${NC}"
pip install --upgrade pip
pip install tensorflow numpy scikit-learn matplotlib pandas tqdm

# Train model
echo -e "${BLUE}Training RNN model...${NC}"
python train_chatbot.py

echo -e "${GREEN}✅ Setup complete! Run: source rnn_env/bin/activate && python chat_with_bot.py${NC}"