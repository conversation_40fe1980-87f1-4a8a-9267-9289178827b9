# 🚀 RNN Chatbot Installation Guide

This guide provides multiple ways to set up your RNN Chatbot with automatic environment setup and dependency installation.

## 📋 Prerequisites

- **Operating System**: Linux, macOS, or Windows
- **Python**: 3.8 or higher
- **Memory**: At least 4GB RAM (8GB+ recommended)
- **Storage**: 2GB free space
- **Internet**: Required for downloading dependencies

## 🎯 Quick Start (Recommended)

### Option 1: Full Auto Setup (Linux/macOS)

```bash
# Make script executable and run
chmod +x auto_setup_rnn.sh
./auto_setup_rnn.sh
```

This script will:
- ✅ Check and install Python if needed
- ✅ Create virtual environment
- ✅ Install system dependencies
- ✅ Install Python packages
- ✅ Train the RNN model
- ✅ Test the model
- ✅ Create startup scripts

### Option 2: Quick Setup (Linux/macOS)

```bash
# For faster setup with minimal checks
chmod +x quick_setup.sh
./quick_setup.sh
```

### Option 3: Windows Auto Setup

```batch
# Double-click or run from command prompt
auto_setup_rnn.bat
```

## 🔧 Manual Installation

If you prefer manual control or the auto scripts don't work:

### Step 1: Check Python

```bash
python3 --version
# Should show Python 3.8 or higher
```

If Python is not installed:

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install python3 python3-pip python3-venv python3-dev
```

**CentOS/RHEL:**
```bash
sudo yum install python3 python3-pip python3-venv python3-devel
```

**macOS:**
```bash
brew install python3
```

**Windows:**
Download from [python.org](https://python.org) and install

### Step 2: Create Virtual Environment

```bash
# Create environment
python3 -m venv rnn_chatbot_env

# Activate environment
# Linux/macOS:
source rnn_chatbot_env/bin/activate
# Windows:
rnn_chatbot_env\Scripts\activate
```

### Step 3: Install Dependencies

```bash
# Upgrade pip
pip install --upgrade pip setuptools wheel

# Install core packages
pip install tensorflow>=2.12.0
pip install numpy>=1.21.0
pip install scikit-learn>=1.0.0
pip install matplotlib>=3.5.0
pip install pandas>=1.3.0
pip install tqdm>=4.64.0
```

### Step 4: Train Model

```bash
python train_chatbot.py
```

### Step 5: Start Chatting

```bash
python chat_with_bot.py
```

## 🐛 Troubleshooting

### Common Issues and Solutions

#### 1. Python Not Found
```bash
# Error: python3: command not found
# Solution: Install Python
sudo apt-get install python3 python3-pip
```

#### 2. Permission Denied
```bash
# Error: Permission denied
# Solution: Make script executable
chmod +x auto_setup_rnn.sh
```

#### 3. TensorFlow Installation Failed
```bash
# Error: Failed building wheel for tensorflow
# Solution: Install CPU version
pip install tensorflow-cpu
```

#### 4. Out of Memory During Training
```python
# Error: ResourceExhaustedError
# Solution: Reduce batch size in train_chatbot.py
batch_size = 32  # Instead of 64
```

#### 5. CUDA/GPU Issues
```bash
# Error: Could not load dynamic library 'libcudart.so'
# Solution: Install CPU version or proper CUDA drivers
pip uninstall tensorflow
pip install tensorflow-cpu
```

#### 6. Virtual Environment Issues
```bash
# Error: venv module not found
# Solution: Install venv module
sudo apt-get install python3-venv
```

### System-Specific Issues

#### Ubuntu/Debian
```bash
# Install build tools
sudo apt-get install build-essential python3-dev
```

#### CentOS/RHEL
```bash
# Install development tools
sudo yum groupinstall "Development Tools"
sudo yum install python3-devel
```

#### macOS
```bash
# Install Xcode command line tools
xcode-select --install
```

#### Windows
- Ensure Python is added to PATH during installation
- Install Microsoft Visual C++ Build Tools if needed

## 📊 Verifying Installation

### Check Python Environment
```bash
# Activate environment
source rnn_chatbot_env/bin/activate  # Linux/macOS
# or
rnn_chatbot_env\Scripts\activate     # Windows

# Check Python version
python --version

# Check installed packages
pip list
```

### Test TensorFlow
```python
python -c "import tensorflow as tf; print('TensorFlow version:', tf.__version__)"
```

### Check Data Files
```bash
ls -la *.json
# Should show your conversation data files
```

### Quick Model Test
```bash
python -c "
from rnn_chatbot import ConversationalAI
print('✅ RNN Chatbot modules loaded successfully!')
"
```

## 🎮 Usage After Installation

### Start Interactive Chat
```bash
# Using startup script (Linux/macOS)
./start_chat.sh

# Using startup script (Windows)
start_chat.bat

# Manual command
source rnn_chatbot_env/bin/activate
python chat_with_bot.py
```

### Run Model Evaluation
```bash
# Using startup script
./run_evaluation.sh  # Linux/macOS
run_evaluation.bat   # Windows

# Manual command
python evaluate_chatbot.py
```

### Retrain Model
```bash
# Using startup script
./retrain_model.sh   # Linux/macOS
retrain_model.bat    # Windows

# Manual command
python train_chatbot.py
```

## 🔄 Environment Management

### Activate Environment
```bash
# Linux/macOS
source rnn_chatbot_env/bin/activate

# Windows
rnn_chatbot_env\Scripts\activate
```

### Deactivate Environment
```bash
deactivate
```

### Remove Environment
```bash
# Remove virtual environment
rm -rf rnn_chatbot_env  # Linux/macOS
rmdir /s rnn_chatbot_env  # Windows
```

### Update Dependencies
```bash
# Activate environment first
source rnn_chatbot_env/bin/activate

# Update packages
pip install --upgrade tensorflow numpy scikit-learn matplotlib pandas
```

## 📈 Performance Optimization

### For Better Performance
```bash
# Install optimized TensorFlow (if available)
pip install tensorflow-gpu  # For NVIDIA GPUs

# Install Intel optimizations (Intel CPUs)
pip install intel-tensorflow
```

### For Lower Memory Usage
```python
# Edit train_chatbot.py
batch_size = 16      # Reduce from 64
max_length = 30      # Reduce from 50
hidden_dim = 256     # Reduce from 512
```

## 🆘 Getting Help

If you encounter issues:

1. **Check the logs**: Look for error messages in the terminal
2. **Verify data files**: Ensure your JSON conversation files exist
3. **Check disk space**: Ensure you have enough free space
4. **Memory issues**: Try reducing batch size or model complexity
5. **Network issues**: Check internet connection for package downloads

### Contact Information
- Check the main README.md for detailed documentation
- Review the troubleshooting section above
- Ensure all prerequisites are met

## 🎉 Success Indicators

You'll know the installation was successful when:

- ✅ Virtual environment is created
- ✅ All dependencies are installed without errors
- ✅ Model training completes successfully
- ✅ Model files are created (trained_chatbot_model.h5, trained_chatbot_tokenizer.pkl)
- ✅ Chat interface starts without errors
- ✅ Bot responds to test messages

**Happy Chatting! 🤖**