#!/bin/bash

# =============================================================================
# Auto Setup Script for RNN Chatbot
# =============================================================================
# This script automatically:
# 1. Checks and sets up Python environment
# 2. Creates virtual environment if needed
# 3. Installs all dependencies
# 4. Trains the RNN model
# 5. Tests the model
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Project settings
PROJECT_DIR="/home/<USER>/gp/BotPack/ai"
VENV_NAME="rnn_chatbot_env"
PYTHON_MIN_VERSION="3.8"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to compare version numbers
version_ge() {
    printf '%s\n%s\n' "$2" "$1" | sort -V -C
}

# Function to get Python version
get_python_version() {
    python3 -c "import sys; print('.'.join(map(str, sys.version_info[:2])))" 2>/dev/null || echo "0.0"
}

# Function to check Python installation
check_python() {
    print_header "🐍 Checking Python Installation"
    echo "=================================================="
    
    if ! command_exists python3; then
        print_error "Python3 is not installed!"
        print_status "Installing Python3..."
        
        # Detect OS and install Python
        if command_exists apt-get; then
            sudo apt-get update
            sudo apt-get install -y python3 python3-pip python3-venv python3-dev
        elif command_exists yum; then
            sudo yum install -y python3 python3-pip python3-venv python3-devel
        elif command_exists brew; then
            brew install python3
        else
            print_error "Cannot install Python automatically. Please install Python 3.8+ manually."
            exit 1
        fi
    fi
    
    PYTHON_VERSION=$(get_python_version)
    print_status "Found Python version: $PYTHON_VERSION"
    
    if ! version_ge "$PYTHON_VERSION" "$PYTHON_MIN_VERSION"; then
        print_error "Python $PYTHON_MIN_VERSION or higher is required. Found: $PYTHON_VERSION"
        exit 1
    fi
    
    print_success "Python check passed ✓"
}

# Function to setup virtual environment
setup_venv() {
    print_header "🏗️  Setting Up Virtual Environment"
    echo "=================================================="
    
    cd "$PROJECT_DIR"
    
    if [ -d "$VENV_NAME" ]; then
        print_status "Virtual environment already exists"
        print_status "Removing old environment..."
        rm -rf "$VENV_NAME"
    fi
    
    print_status "Creating new virtual environment: $VENV_NAME"
    python3 -m venv "$VENV_NAME"
    
    print_status "Activating virtual environment..."
    source "$VENV_NAME/bin/activate"
    
    print_status "Upgrading pip..."
    pip install --upgrade pip setuptools wheel
    
    print_success "Virtual environment setup complete ✓"
}

# Function to install system dependencies
install_system_deps() {
    print_header "📦 Installing System Dependencies"
    echo "=================================================="
    
    if command_exists apt-get; then
        print_status "Installing system packages (Ubuntu/Debian)..."
        sudo apt-get update
        sudo apt-get install -y \
            build-essential \
            python3-dev \
            libhdf5-dev \
            pkg-config \
            libssl-dev \
            libffi-dev \
            libblas-dev \
            liblapack-dev \
            gfortran \
            libfreetype6-dev \
            libpng-dev
    elif command_exists yum; then
        print_status "Installing system packages (CentOS/RHEL)..."
        sudo yum groupinstall -y "Development Tools"
        sudo yum install -y \
            python3-devel \
            hdf5-devel \
            openssl-devel \
            libffi-devel \
            blas-devel \
            lapack-devel \
            gcc-gfortran \
            freetype-devel \
            libpng-devel
    else
        print_warning "Unknown package manager. Some system dependencies might be missing."
    fi
    
    print_success "System dependencies installed ✓"
}

# Function to install Python dependencies
install_python_deps() {
    print_header "🔧 Installing Python Dependencies"
    echo "=================================================="
    
    cd "$PROJECT_DIR"
    source "$VENV_NAME/bin/activate"
    
    print_status "Installing core dependencies..."
    
    # Install dependencies one by one with error handling
    declare -a packages=(
        "numpy>=1.21.0"
        "tensorflow>=2.12.0"
        "scikit-learn>=1.0.0"
        "matplotlib>=3.5.0"
        "seaborn>=0.11.0"
        "pandas>=1.3.0"
        "tqdm>=4.64.0"
        "jupyter>=1.0.0"
    )
    
    for package in "${packages[@]}"; do
        print_status "Installing $package..."
        if pip install "$package"; then
            print_success "$package installed ✓"
        else
            print_warning "Failed to install $package, trying alternative..."
            # Try installing without version constraint
            base_package=$(echo "$package" | cut -d'>' -f1 | cut -d'=' -f1)
            pip install "$base_package" || print_error "Failed to install $base_package"
        fi
    done
    
    # Verify TensorFlow installation
    print_status "Verifying TensorFlow installation..."
    if python3 -c "import tensorflow as tf; print('TensorFlow version:', tf.__version__)" 2>/dev/null; then
        print_success "TensorFlow verification passed ✓"
    else
        print_error "TensorFlow installation failed!"
        print_status "Trying CPU-only TensorFlow..."
        pip install tensorflow-cpu
    fi
    
    print_success "Python dependencies installed ✓"
}

# Function to check data files
check_data_files() {
    print_header "📊 Checking Data Files"
    echo "=================================================="
    
    cd "$PROJECT_DIR"
    
    declare -a data_files=(
        "final_1m_simsimi.json"
        "cleaned_natural_simsimi.json"
        "100k_asks_500k_answers.json"
        "massive_250k_2m_simsimi.json"
        "simsimi.json"
    )
    
    found_files=0
    for file in "${data_files[@]}"; do
        if [ -f "$file" ]; then
            print_success "Found: $file ✓"
            ((found_files++))
        else
            print_warning "Missing: $file"
        fi
    done
    
    if [ $found_files -eq 0 ]; then
        print_error "No data files found! Please ensure conversation data is available."
        exit 1
    fi
    
    print_success "Found $found_files data files for training ✓"
}

# Function to train RNN model
train_model() {
    print_header "🚀 Training RNN Model"
    echo "=================================================="
    
    cd "$PROJECT_DIR"
    source "$VENV_NAME/bin/activate"
    
    print_status "Starting RNN model training..."
    print_status "This may take 30-60 minutes depending on your hardware..."
    
    # Check if training script exists
    if [ ! -f "train_chatbot.py" ]; then
        print_error "Training script not found!"
        exit 1
    fi
    
    # Run training with progress indication
    print_status "Executing: python train_chatbot.py"
    if python train_chatbot.py; then
        print_success "Model training completed successfully! ✓"
    else
        print_error "Model training failed!"
        exit 1
    fi
}

# Function to test the model
test_model() {
    print_header "🧪 Testing Trained Model"
    echo "=================================================="
    
    cd "$PROJECT_DIR"
    source "$VENV_NAME/bin/activate"
    
    # Check if model files exist
    if [ -f "trained_chatbot_model.h5" ] && [ -f "trained_chatbot_tokenizer.pkl" ]; then
        print_success "Model files found ✓"
        
        print_status "Running quick model test..."
        
        # Create a quick test script
        cat > quick_test.py << 'EOF'
#!/usr/bin/env python3
import sys
sys.path.append('.')

try:
    from rnn_chatbot import ConversationalAI
    
    print("Loading trained model...")
    ai = ConversationalAI(data_files=[], vocab_size=20000, max_length=50, embedding_dim=256, hidden_dim=512)
    ai.load_model("trained_chatbot")
    
    print("Testing model with sample questions...")
    test_questions = ["hello", "how are you", "what is your name"]
    
    for question in test_questions:
        try:
            response = ai.generate_response(question, temperature=0.7)
            print(f"Q: {question}")
            print(f"A: {response}")
            print("-" * 30)
        except Exception as e:
            print(f"Error testing '{question}': {e}")
    
    print("✅ Model test completed successfully!")
    
except Exception as e:
    print(f"❌ Model test failed: {e}")
    sys.exit(1)
EOF
        
        if python quick_test.py; then
            print_success "Model test passed ✓"
            rm -f quick_test.py
        else
            print_error "Model test failed!"
            rm -f quick_test.py
            exit 1
        fi
    else
        print_error "Model files not found!"
        exit 1
    fi
}

# Function to create startup scripts
create_startup_scripts() {
    print_header "📝 Creating Startup Scripts"
    echo "=================================================="
    
    cd "$PROJECT_DIR"
    
    # Create chat startup script
    cat > start_chat.sh << EOF
#!/bin/bash
cd "$PROJECT_DIR"
source "$VENV_NAME/bin/activate"
python chat_with_bot.py
EOF
    chmod +x start_chat.sh
    
    # Create evaluation startup script
    cat > run_evaluation.sh << EOF
#!/bin/bash
cd "$PROJECT_DIR"
source "$VENV_NAME/bin/activate"
python evaluate_chatbot.py
EOF
    chmod +x run_evaluation.sh
    
    # Create retrain script
    cat > retrain_model.sh << EOF
#!/bin/bash
cd "$PROJECT_DIR"
source "$VENV_NAME/bin/activate"
python train_chatbot.py
EOF
    chmod +x retrain_model.sh
    
    print_success "Startup scripts created ✓"
}

# Function to display final instructions
show_final_instructions() {
    print_header "🎉 Setup Complete!"
    echo "=================================================="
    
    print_success "RNN Chatbot setup completed successfully!"
    echo ""
    print_status "Available commands:"
    echo "  🗣️  Start chatting:     ./start_chat.sh"
    echo "  📊 Run evaluation:     ./run_evaluation.sh"
    echo "  🔄 Retrain model:      ./retrain_model.sh"
    echo ""
    print_status "Manual commands:"
    echo "  Activate environment:  source $VENV_NAME/bin/activate"
    echo "  Train model:          python train_chatbot.py"
    echo "  Start chat:           python chat_with_bot.py"
    echo "  Evaluate model:       python evaluate_chatbot.py"
    echo ""
    print_status "Model files created:"
    echo "  📁 trained_chatbot_model.h5"
    echo "  📁 trained_chatbot_tokenizer.pkl"
    echo ""
    print_success "🚀 Your RNN Chatbot is ready to use!"
}

# Main execution function
main() {
    clear
    print_header "🤖 RNN Chatbot Auto Setup Script"
    print_header "=================================="
    echo ""
    print_status "Starting automatic setup process..."
    echo ""
    
    # Check if running from correct directory
    if [ ! -f "rnn_chatbot.py" ]; then
        print_error "Please run this script from the project directory containing rnn_chatbot.py"
        exit 1
    fi
    
    # Execute setup steps
    check_python
    echo ""
    
    install_system_deps
    echo ""
    
    setup_venv
    echo ""
    
    install_python_deps
    echo ""
    
    check_data_files
    echo ""
    
    train_model
    echo ""
    
    test_model
    echo ""
    
    create_startup_scripts
    echo ""
    
    show_final_instructions
}

# Handle script interruption
trap 'print_error "Setup interrupted by user"; exit 1' INT TERM

# Run main function
main "$@"