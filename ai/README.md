# 🤖 Advanced RNN Conversational AI

A sophisticated conversational AI chatbot built with RNN (LSTM/GRU) architecture, featuring advanced tokenization, grammar correction, attention mechanisms, and multi-language support.

## 🌟 Features

### Core AI Capabilities
- **Advanced RNN Architecture**: Bidirectional LSTM with attention mechanism
- **Smart Tokenization**: Subword tokenization with vocabulary optimization
- **Grammar Correction**: Automatic grammar fixing and text cleaning
- **Context Awareness**: Attention mechanism for better context understanding
- **Response Diversity**: Temperature-based sampling for varied responses
- **Multi-language Support**: English, Bengali, and Banglish support

### Technical Features
- **Seq2Seq Model**: Encoder-decoder architecture with attention
- **Beam Search**: Advanced decoding for better response quality
- **Layer Normalization**: Improved training stability
- **Dropout Regularization**: Prevents overfitting
- **Early Stopping**: Automatic training optimization
- **Model Checkpointing**: Save best performing models

## 📁 Project Structure

```
/home/<USER>/gp/BotPack/ai/
├── rnn_chatbot.py          # Main chatbot implementation
├── train_chatbot.py        # Training script
├── chat_with_bot.py        # Interactive chat interface
├── evaluate_chatbot.py     # Model evaluation and testing
├── setup.py               # Environment setup script
├── requirements.txt       # Python dependencies
├── README.md             # This file
└── Data files:
    ├── final_1m_simsimi.json
    ├── cleaned_natural_simsimi.json
    ├── 100k_asks_500k_answers.json
    └── other conversation datasets
```

## 🚀 Quick Start

### 1. Setup Environment

```bash
# Install dependencies
python setup.py

# Or manually install requirements
pip install -r requirements.txt
```

### 2. Train the Model

```bash
# Train the chatbot (this may take 30-60 minutes)
python train_chatbot.py
```

Training features:
- Loads multiple conversation datasets
- Advanced preprocessing and tokenization
- Attention-based RNN training
- Automatic model saving
- Validation and early stopping

### 3. Start Chatting

```bash
# Start interactive chat
python chat_with_bot.py
```

Chat features:
- Real-time conversation
- Grammar correction
- Context-aware responses
- Multi-language support
- Smart response selection

### 4. Evaluate Performance

```bash
# Run comprehensive evaluation
python evaluate_chatbot.py
```

Evaluation includes:
- Basic conversation testing
- Grammar correction testing
- Response diversity analysis
- Vocabulary usage analysis
- Multi-language support testing

## 🧠 Model Architecture

### RNN Components
```
Input → Tokenizer → Encoder (Bi-LSTM) → Attention → Decoder (LSTM) → Output
```

### Key Components:

1. **Advanced Tokenizer**
   - Vocabulary building with frequency analysis
   - Grammar correction patterns
   - Special token handling (PAD, START, END, UNK)
   - Text cleaning and normalization

2. **Encoder Network**
   - Bidirectional LSTM layers
   - Embedding layer with masking
   - Dropout for regularization
   - Context vector generation

3. **Attention Mechanism**
   - Attention layer for context focus
   - Dynamic context vector computation
   - Improved response relevance

4. **Decoder Network**
   - LSTM decoder with attention
   - Layer normalization
   - Dense output layers with softmax
   - Beam search for inference

## 📊 Data Format

The model expects conversation data in JSON format:

```json
[
  {
    "ask": "hello",
    "ans": [
      "hello there",
      "hi how are you",
      "hey what's up"
    ]
  },
  {
    "ask": "how are you",
    "ans": [
      "I'm doing great",
      "I'm fine thanks",
      "Pretty good, you?"
    ]
  }
]
```

## ⚙️ Configuration

### Model Parameters

```python
# In train_chatbot.py, you can adjust:
vocab_size = 20000      # Vocabulary size
max_length = 50         # Maximum sequence length
embedding_dim = 256     # Embedding dimensions
hidden_dim = 512        # Hidden layer size
epochs = 20            # Training epochs
batch_size = 64        # Batch size
```

### Training Parameters

```python
# Advanced training options:
validation_split = 0.2  # Validation data percentage
learning_rate = 0.001   # Learning rate
dropout_rate = 0.3      # Dropout rate
temperature = 0.7       # Response sampling temperature
```

## 🎯 Usage Examples

### Basic Training
```python
from rnn_chatbot import ConversationalAI

# Initialize
ai = ConversationalAI(
    data_files=["your_data.json"],
    vocab_size=20000,
    max_length=50
)

# Train
ai.load_data()
ai.train(epochs=20, batch_size=64)

# Save model
ai.save_model("my_chatbot")
```

### Interactive Chat
```python
# Load trained model
ai.load_model("my_chatbot")

# Generate response
response = ai.generate_response("Hello, how are you?")
print(response)

# Start interactive chat
ai.chat()
```

### Custom Response Generation
```python
# Generate with different parameters
response = ai.generate_response(
    question="Tell me a joke",
    temperature=0.9,    # More creative
    top_k=10           # Consider top 10 tokens
)
```

## 🔧 Advanced Features

### Grammar Correction
The tokenizer automatically corrects common grammar issues:
- `i` → `I`
- `u` → `you`
- `ur` → `your`
- `im` → `I'm`
- `cant` → `can't`
- And many more...

### Multi-language Support
Supports mixed language conversations:
- English: "Hello, how are you?"
- Bengali: "কেমন আছেন?"
- Banglish: "ki khobor, kemon acho?"

### Response Quality Features
- **Temperature Sampling**: Control response creativity
- **Top-k Sampling**: Focus on most likely tokens
- **Beam Search**: Generate multiple candidates
- **Response Filtering**: Remove low-quality responses

## 📈 Performance Optimization

### Training Tips
1. **Data Quality**: Clean and diverse conversation data
2. **Vocabulary Size**: Balance between coverage and efficiency
3. **Sequence Length**: Optimize for your use case
4. **Batch Size**: Adjust based on available memory
5. **Learning Rate**: Use learning rate scheduling

### Inference Optimization
1. **Model Quantization**: Reduce model size
2. **Caching**: Cache frequent responses
3. **Batch Inference**: Process multiple queries together
4. **GPU Acceleration**: Use GPU for faster inference

## 🐛 Troubleshooting

### Common Issues

1. **Out of Memory Error**
   ```python
   # Reduce batch size or sequence length
   batch_size = 32  # Instead of 64
   max_length = 30  # Instead of 50
   ```

2. **Poor Response Quality**
   ```python
   # Increase model complexity or training data
   hidden_dim = 1024  # Larger hidden size
   epochs = 50       # More training epochs
   ```

3. **Training Too Slow**
   ```python
   # Use GPU acceleration
   import tensorflow as tf
   gpus = tf.config.experimental.list_physical_devices('GPU')
   if gpus:
       tf.config.experimental.set_memory_growth(gpus[0], True)
   ```

### Error Solutions

- **CUDA/GPU Issues**: Install appropriate TensorFlow GPU version
- **Memory Issues**: Reduce batch size or use gradient accumulation
- **Convergence Issues**: Adjust learning rate or add regularization
- **Data Loading Issues**: Check JSON format and file encoding

## 🤝 Contributing

Feel free to contribute improvements:

1. **Data Enhancement**: Add more conversation datasets
2. **Model Improvements**: Implement newer architectures (Transformers)
3. **Feature Additions**: Add new capabilities
4. **Performance Optimization**: Improve speed and efficiency
5. **Bug Fixes**: Report and fix issues

## 📝 License

This project is open source. Feel free to use, modify, and distribute.

## 🙏 Acknowledgments

- TensorFlow team for the deep learning framework
- SimSimi dataset contributors
- Open source NLP community
- Bengali language processing community

---

## 🎉 Happy Chatting!

Your advanced RNN conversational AI is ready to chat! The model combines state-of-the-art NLP techniques with practical features for real-world deployment.

For questions or support, please check the troubleshooting section or create an issue in the repository.