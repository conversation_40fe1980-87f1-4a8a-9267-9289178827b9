# Install required packages
!pip install tensorflow>=2.13.0
!pip install numpy pandas matplotlib seaborn
!pip install scikit-learn
!pip install tqdm

# Import libraries
import os
import json
import zipfile
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm
import logging
import pickle
import re
from typing import List, Dict, Tuple, Optional
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# TensorFlow imports
import tensorflow as tf
from tensorflow.keras.models import Model
from tensorflow.keras.layers import (
    Input, LSTM, Dense, Embedding, Dropout, 
    LayerNormalization, Bidirectional, Dot, Activation, Softmax
)
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau
from sklearn.model_selection import train_test_split

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

print("✅ All libraries imported successfully!")

def setup_gpu():
    """Configure GPU settings for optimal performance"""
    print("🔍 Checking GPU availability...")
    
    # Check if GPU is available
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        try:
            # Enable memory growth to avoid allocating all GPU memory at once
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print(f"✅ Found {len(gpus)} GPU(s): {[gpu.name for gpu in gpus]}")
            print("🚀 GPU memory growth enabled")
        except RuntimeError as e:
            print(f"❌ GPU setup error: {e}")
    else:
        print("⚠️ No GPU found, using CPU")
    
    # Set mixed precision for better performance
    try:
        policy = tf.keras.mixed_precision.Policy('mixed_float16')
        tf.keras.mixed_precision.set_global_policy(policy)
        print("✅ Mixed precision enabled for better performance")
    except:
        print("⚠️ Mixed precision not available")
    
    return len(gpus) > 0

# Setup GPU
has_gpu = setup_gpu()

from google.colab import files

# Upload the zip file
print("📁 Please upload your chatbot_data.zip file:")
uploaded = files.upload()

# Check if zip file was uploaded
zip_files = [f for f in uploaded.keys() if f.endswith('.zip')]
if zip_files:
    print(f"✅ Uploaded: {zip_files[0]}")
else:
    print("❌ No zip file uploaded. Please upload chatbot_data.zip")

def extract_data_files():
    """Extract data files from uploaded zip"""
    print("📁 Looking for data files...")
    
    # Check if zip file exists
    zip_files = [f for f in os.listdir('.') if f.endswith('.zip') and 'chatbot' in f.lower()]
    
    if not zip_files:
        print("❌ No chatbot data zip file found!")
        print("Please upload 'chatbot_data.zip' to Colab")
        return False
    
    zip_file = zip_files[0]
    print(f"📦 Found zip file: {zip_file}")
    
    # Extract files
    with zipfile.ZipFile(zip_file, 'r') as zip_ref:
        zip_ref.extractall('.')
        extracted_files = zip_ref.namelist()
    
    json_files = [f for f in extracted_files if f.endswith('.json')]
    print(f"✅ Extracted {len(json_files)} JSON files:")
    for file in json_files:
        size_mb = os.path.getsize(file) / (1024 * 1024)
        print(f"  - {file} ({size_mb:.1f} MB)")
    
    return json_files

# Extract data files
data_files = extract_data_files()

# Copy the AdvancedTokenizer class from the Python file
# (This cell would contain the full AdvancedTokenizer class code)

class AdvancedTokenizer:
    """Advanced tokenizer with grammar correction and vocabulary optimization"""
    
    def __init__(self, vocab_size: int = 20000, max_length: int = 50):
        self.vocab_size = vocab_size
        self.max_length = max_length
        self.word_to_idx = {}
        self.idx_to_word = {}
        
        # Special tokens
        self.PAD_TOKEN = '<PAD>'
        self.START_TOKEN = '<START>'
        self.END_TOKEN = '<END>'
        self.UNK_TOKEN = '<UNK>'
        
        # Grammar correction patterns
        self.grammar_patterns = [
            (r'\bi\b', 'I'),  # lowercase i to uppercase I
            (r'\bu\b', 'you'),  # u to you
            (r'\bur\b', 'your'),  # ur to your
            (r'\br\b', 'are'),  # r to are
            (r'\bn\b', 'and'),  # n to and
            (r'\bthx\b', 'thanks'),  # thx to thanks
            (r'\bpls\b', 'please'),  # pls to please
        ]
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        if not text:
            return ""
        
        # Convert to lowercase
        text = text.lower().strip()
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Apply grammar corrections
        for pattern, replacement in self.grammar_patterns:
            text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)
        
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s.,!?-]', '', text)
        
        return text.strip()
    
    def build_vocab(self, texts: List[str]):
        """Build vocabulary from texts"""
        logger.info("Building vocabulary...")
        
        # Add special tokens
        special_tokens = [self.PAD_TOKEN, self.START_TOKEN, self.END_TOKEN, self.UNK_TOKEN]
        
        # Count word frequencies
        word_freq = Counter()
        for text in tqdm(texts, desc="Processing texts"):
            cleaned_text = self.clean_text(text)
            words = cleaned_text.split()
            word_freq.update(words)
        
        # Get most common words
        most_common = word_freq.most_common(self.vocab_size - len(special_tokens))
        
        # Build vocabulary
        vocab = special_tokens + [word for word, _ in most_common]
        
        self.word_to_idx = {word: idx for idx, word in enumerate(vocab)}
        self.idx_to_word = {idx: word for word, idx in self.word_to_idx.items()}
        
        logger.info(f"Vocabulary built with {len(self.word_to_idx)} tokens")
        return self.word_to_idx
    
    def texts_to_sequences(self, texts: List[str]) -> List[List[int]]:
        """Convert texts to sequences of token indices"""
        sequences = []
        for text in texts:
            cleaned_text = self.clean_text(text)
            words = cleaned_text.split()
            sequence = [self.word_to_idx.get(word, self.word_to_idx[self.UNK_TOKEN]) for word in words]
            sequences.append(sequence)
        return sequences
    
    def pad_sequences(self, sequences: List[List[int]]) -> np.ndarray:
        """Pad sequences to max_length"""
        padded = np.full((len(sequences), self.max_length), self.word_to_idx[self.PAD_TOKEN])
        
        for i, seq in enumerate(sequences):
            length = min(len(seq), self.max_length)
            padded[i, :length] = seq[:length]
        
        return padded

print("✅ AdvancedTokenizer class defined!")

class AttentionRNNChatbot:
    """Advanced RNN Chatbot with Custom Attention Mechanism"""
    
    def __init__(self, vocab_size: int, embedding_dim: int = 256, 
                 hidden_dim: int = 512, max_length: int = 50):
        self.vocab_size = vocab_size
        self.embedding_dim = embedding_dim
        self.hidden_dim = hidden_dim
        self.max_length = max_length
        self.model = None
        self.encoder_model = None
        self.decoder_model = None
    
    def build_model(self):
        """Build the seq2seq model with custom attention"""
        logger.info("Building RNN model with attention...")
        
        # Encoder
        encoder_inputs = Input(shape=(self.max_length,), name='encoder_inputs')
        encoder_embedding = Embedding(self.vocab_size, self.embedding_dim, 
                                    mask_zero=True, name='encoder_embedding')(encoder_inputs)
        
        # Bidirectional LSTM encoder
        encoder_lstm = Bidirectional(LSTM(self.hidden_dim, return_sequences=True, 
                                        return_state=True, dropout=0.3, 
                                        recurrent_dropout=0.3), name='encoder_lstm')
        encoder_outputs, forward_h, forward_c, backward_h, backward_c = encoder_lstm(encoder_embedding)
        
        # Combine forward and backward states
        state_h = tf.keras.layers.Concatenate()([forward_h, backward_h])
        state_c = tf.keras.layers.Concatenate()([forward_c, backward_c])
        encoder_states = [state_h, state_c]
        
        # Decoder
        decoder_inputs = Input(shape=(self.max_length,), name='decoder_inputs')
        decoder_embedding = Embedding(self.vocab_size, self.embedding_dim, 
                                    mask_zero=True, name='decoder_embedding')(decoder_inputs)
        
        decoder_lstm = LSTM(self.hidden_dim * 2, return_sequences=True, 
                          return_state=True, dropout=0.3, 
                          recurrent_dropout=0.3, name='decoder_lstm')
        decoder_outputs, _, _ = decoder_lstm(decoder_embedding, initial_state=encoder_states)
        
        # Custom attention mechanism
        attention_scores = Dot(axes=[2, 2], name='attention_scores')([decoder_outputs, encoder_outputs])
        attention_weights = Softmax(axis=-1, name='attention_weights')(attention_scores)
        context_vector = Dot(axes=[2, 1], name='attention_context')([attention_weights, encoder_outputs])
        
        # Combine decoder output with attention context
        decoder_combined = tf.keras.layers.Concatenate(axis=-1)([decoder_outputs, context_vector])
        decoder_combined = LayerNormalization()(decoder_combined)
        
        # Dense layers for output
        decoder_dense1 = Dense(self.hidden_dim, activation='relu', name='decoder_dense1')(decoder_combined)
        decoder_dropout = Dropout(0.3)(decoder_dense1)
        decoder_outputs_final = Dense(self.vocab_size, activation='softmax', 
                                    name='decoder_outputs', dtype='float32')(decoder_dropout)
        
        # Create model
        self.model = Model([encoder_inputs, decoder_inputs], decoder_outputs_final)
        
        # Compile with mixed precision considerations
        optimizer = Adam(learning_rate=0.001)
        self.model.compile(optimizer=optimizer, 
                          loss='sparse_categorical_crossentropy',
                          metrics=['accuracy'])
        
        logger.info("Model built successfully!")
        return self.model

print("✅ AttentionRNNChatbot class defined!")

# Training Configuration
config = {
    'vocab_size': 20000,
    'max_length': 50,
    'embedding_dim': 256,
    'hidden_dim': 512,
    'epochs': 20,
    'batch_size': 64 if has_gpu else 32,  # Adjust batch size based on GPU availability
    'validation_split': 0.2
}

print("📊 Training Configuration:")
for key, value in config.items():
    print(f"  {key}: {value}")
print(f"\n⚡ Using: {'GPU' if has_gpu else 'CPU'}")

# Load and prepare data
def load_conversation_data(data_files):
    """Load conversation data from JSON files"""
    all_questions = []
    all_answers = []
    
    for file_path in tqdm(data_files, desc="Loading files"):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            file_questions = []
            file_answers = []
            
            for item in tqdm(data, desc=f"Processing {file_path}", leave=False):
                question = item.get('ask', '').strip()
                answers = item.get('ans', [])
                
                if question and answers:
                    if isinstance(answers, list):
                        for answer in answers:
                            if answer.strip():
                                file_questions.append(question)
                                file_answers.append(answer.strip())
                    else:
                        file_questions.append(question)
                        file_answers.append(str(answers).strip())
            
            all_questions.extend(file_questions)
            all_answers.extend(file_answers)
            
            print(f"✅ Loaded {len(file_questions):,} conversations from {file_path}")
            
        except Exception as e:
            print(f"❌ Error loading {file_path}: {e}")
            continue
    
    print(f"\n📊 Total loaded: {len(all_questions):,} question-answer pairs")
    return all_questions, all_answers

if data_files:
    questions, answers = load_conversation_data(data_files)
else:
    print("❌ No data files available. Please upload chatbot_data.zip first!")

# Train the model
if 'questions' in locals() and 'answers' in locals():
    print("🚀 Starting model training...")
    
    # Initialize tokenizer
    tokenizer = AdvancedTokenizer(config['vocab_size'], config['max_length'])
    
    # Build vocabulary
    all_texts = questions + answers
    tokenizer.build_vocab(all_texts)
    
    # Prepare training data
    print("📊 Preparing training data...")
    question_sequences = tokenizer.texts_to_sequences(questions)
    answer_sequences = tokenizer.texts_to_sequences(answers)
    
    encoder_input_data = tokenizer.pad_sequences(question_sequences)
    
    # Prepare decoder data
    decoder_input_sequences = []
    decoder_target_sequences = []
    
    start_token_idx = tokenizer.word_to_idx[tokenizer.START_TOKEN]
    end_token_idx = tokenizer.word_to_idx[tokenizer.END_TOKEN]
    
    for seq in tqdm(answer_sequences, desc="Preparing decoder data"):
        decoder_input = [start_token_idx] + seq
        decoder_target = seq + [end_token_idx]
        decoder_input_sequences.append(decoder_input)
        decoder_target_sequences.append(decoder_target)
    
    decoder_input_data = tokenizer.pad_sequences(decoder_input_sequences)
    decoder_target_data = tokenizer.pad_sequences(decoder_target_sequences)
    
    # Split data
    indices = np.arange(len(encoder_input_data))
    train_indices, val_indices = train_test_split(indices, test_size=config['validation_split'], random_state=42)
    
    train_encoder = encoder_input_data[train_indices]
    train_decoder_input = decoder_input_data[train_indices]
    train_decoder_target = decoder_target_data[train_indices]
    
    val_encoder = encoder_input_data[val_indices]
    val_decoder_input = decoder_input_data[val_indices]
    val_decoder_target = decoder_target_data[val_indices]
    
    print(f"📊 Training samples: {len(train_encoder):,}")
    print(f"📊 Validation samples: {len(val_encoder):,}")
    
    # Build model
    chatbot = AttentionRNNChatbot(len(tokenizer.word_to_idx), 
                                 config['embedding_dim'], 
                                 config['hidden_dim'], 
                                 config['max_length'])
    model = chatbot.build_model()
    
    # Display model summary
    print("\n📊 Model Architecture:")
    model.summary()
    
    # Callbacks
    callbacks = [
        EarlyStopping(patience=5, restore_best_weights=True, verbose=1),
        ModelCheckpoint('best_chatbot_model.h5', save_best_only=True, verbose=1),
        ReduceLROnPlateau(factor=0.5, patience=3, min_lr=0.0001, verbose=1)
    ]
    
    # Train model
    print(f"\n🚀 Starting training with {config['epochs']} epochs...")
    history = model.fit(
        [train_encoder, train_decoder_input], train_decoder_target,
        batch_size=config['batch_size'],
        epochs=config['epochs'],
        validation_data=([val_encoder, val_decoder_input], val_decoder_target),
        callbacks=callbacks,
        verbose=1
    )
    
    print("\n✅ Training completed!")
    
else:
    print("❌ No data loaded. Please run the data loading cell first!")

# Plot training results
if 'history' in locals():
    plt.figure(figsize=(15, 5))
    
    # Plot loss
    plt.subplot(1, 3, 1)
    plt.plot(history.history['loss'], label='Training Loss')
    plt.plot(history.history['val_loss'], label='Validation Loss')
    plt.title('Model Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)
    
    # Plot accuracy
    plt.subplot(1, 3, 2)
    plt.plot(history.history['accuracy'], label='Training Accuracy')
    plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
    plt.title('Model Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.legend()
    plt.grid(True)
    
    # Plot learning rate
    plt.subplot(1, 3, 3)
    if 'lr' in history.history:
        plt.plot(history.history['lr'], label='Learning Rate')
        plt.title('Learning Rate')
        plt.xlabel('Epoch')
        plt.ylabel('Learning Rate')
        plt.legend()
        plt.grid(True)
    
    plt.tight_layout()
    plt.show()
    
    print("📊 Training metrics plotted successfully!")
else:
    print("❌ No training history available. Please train the model first!")

# Save the trained model
if 'model' in locals() and 'tokenizer' in locals():
    print("💾 Saving trained model and tokenizer...")
    
    # Save model
    model.save('colab_trained_chatbot.h5')
    
    # Save tokenizer
    with open('colab_trained_chatbot_tokenizer.pkl', 'wb') as f:
        pickle.dump(tokenizer, f)
    
    print("✅ Model saved as colab_trained_chatbot.h5")
    print("✅ Tokenizer saved as colab_trained_chatbot_tokenizer.pkl")
    
    # Download files
    print("\n📥 Downloading files...")
    files.download('colab_trained_chatbot.h5')
    files.download('colab_trained_chatbot_tokenizer.pkl')
    
    print("🎉 Training completed successfully!")
    print("📁 Files have been downloaded to your computer.")
    
else:
    print("❌ No trained model available. Please train the model first!")