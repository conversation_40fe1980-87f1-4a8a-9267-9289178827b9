{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# 🤖 <PERSON><PERSON><PERSON> - Zero Dependency Conflicts Version\n", "\n", "This version uses **ONLY** Colab's pre-installed packages to avoid any dependency conflicts.\n", "\n", "## ✅ No Package Installation Required!\n", "- Uses Colab's default TensorFlow\n", "- Uses Colab's default NumPy\n", "- No version conflicts\n", "- Maximum compatibility\n", "\n", "## 📋 Instructions:\n", "1. **Enable GPU**: Runtime → Change runtime type → GPU\n", "2. **Upload data**: Upload `chatbot_data.zip` when prompted\n", "3. **Run all cells**: No package installation needed!\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## 📦 Import Pre-installed Libraries Only"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "imports"}, "outputs": [], "source": ["# Import only pre-installed Colab libraries\n", "import os\n", "import json\n", "import zipfile\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import re\n", "import gc\n", "import pickle\n", "from collections import Counter\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# TensorFlow (pre-installed in Colab)\n", "import tensorflow as tf\n", "from tensorflow.keras.models import Model\n", "from tensorflow.keras.layers import (\n", "    Input, LSTM, Dense, Embedding, Dropout, \n", "    LayerNormalization, Bidirectional, Dot, Softmax\n", ")\n", "from tensorflow.keras.optimizers import Adam\n", "from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau\n", "\n", "print(f\"✅ TensorFlow version: {tf.__version__}\")\n", "print(f\"✅ NumPy version: {np.__version__}\")\n", "print(\"✅ All libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "gpu_setup"}, "source": ["## 🔧 GPU Setup (No Dependencies)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "gpu_config"}, "outputs": [], "source": ["def setup_gpu_simple():\n", "    \"\"\"Simple GPU setup using only TensorFlow\"\"\"\n", "    print(\"🔍 Checking GPU availability...\")\n", "    \n", "    gpus = tf.config.experimental.list_physical_devices('GPU')\n", "    if gpus:\n", "        try:\n", "            for gpu in gpus:\n", "                tf.config.experimental.set_memory_growth(gpu, True)\n", "            print(f\"✅ Found {len(gpus)} GPU(s)\")\n", "            print(\"🚀 GPU memory growth enabled\")\n", "        except RuntimeError as e:\n", "            print(f\"⚠️ GPU setup warning: {e}\")\n", "    else:\n", "        print(\"⚠️ No GPU found, using CPU\")\n", "    \n", "    return len(gpus) > 0\n", "\n", "def get_simple_config():\n", "    \"\"\"Get a simple, safe configuration\"\"\"\n", "    # Conservative settings that work on most Colab instances\n", "    config = {\n", "        'vocab_size': 8000,\n", "        'max_length': 30,\n", "        'embedding_dim': 128,\n", "        'hidden_dim': 256,\n", "        'epochs': 10,\n", "        'batch_size': 16,\n", "        'validation_split': 0.2,\n", "        'sample_size': 50000  # Limit data for stability\n", "    }\n", "    return config\n", "\n", "# Setup\n", "has_gpu = setup_gpu_simple()\n", "config = get_simple_config()\n", "\n", "print(f\"\\n📊 Configuration:\")\n", "for key, value in config.items():\n", "    print(f\"  {key}: {value}\")\n", "\n", "print(f\"\\n⚡ Using: {'GPU' if has_gpu else 'CPU'}\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_upload"}, "source": ["## 📁 Data Upload and Extraction"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upload_data"}, "outputs": [], "source": ["from google.colab import files\n", "\n", "# Upload the zip file\n", "print(\"📁 Please upload your chatbot_data.zip file:\")\n", "uploaded = files.upload()\n", "\n", "# Check if zip file was uploaded\n", "zip_files = [f for f in uploaded.keys() if f.endswith('.zip')]\n", "if zip_files:\n", "    print(f\"✅ Uploaded: {zip_files[0]}\")\n", "else:\n", "    print(\"❌ No zip file uploaded. Please upload chatbot_data.zip\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "extract_data"}, "outputs": [], "source": ["def extract_and_load_data(config):\n", "    \"\"\"Extract and load data in one step\"\"\"\n", "    print(\"📁 Extracting and loading data...\")\n", "    \n", "    # Find zip file\n", "    zip_files = [f for f in os.listdir('.') if f.endswith('.zip') and 'chatbot' in f.lower()]\n", "    if not zip_files:\n", "        print(\"❌ No chatbot data zip file found!\")\n", "        return [], []\n", "    \n", "    zip_file = zip_files[0]\n", "    print(f\"📦 Processing: {zip_file}\")\n", "    \n", "    all_questions = []\n", "    all_answers = []\n", "    \n", "    try:\n", "        with zipfile.ZipFile(zip_file, 'r') as zip_ref:\n", "            json_files = [f for f in zip_ref.namelist() if f.endswith('.json')]\n", "            print(f\"📊 Found {len(json_files)} JSON files\")\n", "            \n", "            # Process files directly from zip (saves memory)\n", "            for json_file in json_files[:3]:  # Limit to 3 files for stability\n", "                print(f\"📖 Processing {json_file}...\")\n", "                \n", "                with zip_ref.open(json_file) as f:\n", "                    data = json.load(f)\n", "                \n", "                file_count = 0\n", "                for item in data:\n", "                    if file_count >= 15000:  # Limit per file\n", "                        break\n", "                        \n", "                    question = item.get('ask', '').strip()\n", "                    answers = item.get('ans', [])\n", "                    \n", "                    if question and answers:\n", "                        if isinstance(answers, list) and answers:\n", "                            all_questions.append(question)\n", "                            all_answers.append(answers[0].strip())\n", "                            file_count += 1\n", "                        elif isinstance(answers, str) and answers.strip():\n", "                            all_questions.append(question)\n", "                            all_answers.append(answers.strip())\n", "                            file_count += 1\n", "                    \n", "                    # Check global limit\n", "                    if len(all_questions) >= config['sample_size']:\n", "                        break\n", "                \n", "                print(f\"✅ Loaded {file_count:,} from {json_file}\")\n", "                \n", "                # Clean up\n", "                del data\n", "                gc.collect()\n", "                \n", "                if len(all_questions) >= config['sample_size']:\n", "                    break\n", "    \n", "    except Exception as e:\n", "        print(f\"❌ Error processing data: {e}\")\n", "        return [], []\n", "    \n", "    # Trim to exact sample size\n", "    if len(all_questions) > config['sample_size']:\n", "        all_questions = all_questions[:config['sample_size']]\n", "        all_answers = all_answers[:config['sample_size']]\n", "    \n", "    print(f\"📊 Final dataset: {len(all_questions):,} conversation pairs\")\n", "    return all_questions, all_answers\n", "\n", "# Load data\n", "questions, answers = extract_and_load_data(config)\n", "\n", "if len(questions) > 0:\n", "    print(f\"✅ Data loaded successfully: {len(questions):,} samples\")\n", "    print(f\"📝 Sample question: {questions[0][:50]}...\")\n", "    print(f\"📝 Sample answer: {answers[0][:50]}...\")\n", "else:\n", "    print(\"❌ No data loaded. Please check your zip file.\")"]}, {"cell_type": "markdown", "metadata": {"id": "tokenizer"}, "source": ["## 🔤 Simple Tokenizer (No Dependencies)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "simple_tokenizer"}, "outputs": [], "source": ["class SimpleTokenizer:\n", "    \"\"\"Simple tokenizer using only built-in Python\"\"\"\n", "    \n", "    def __init__(self, vocab_size=8000, max_length=30):\n", "        self.vocab_size = vocab_size\n", "        self.max_length = max_length\n", "        self.word_to_idx = {}\n", "        self.idx_to_word = {}\n", "        \n", "        self.PAD_TOKEN = '<PAD>'\n", "        self.START_TOKEN = '<START>'\n", "        self.END_TOKEN = '<END>'\n", "        self.UNK_TOKEN = '<UNK>'\n", "    \n", "    def clean_text(self, text):\n", "        \"\"\"Simple text cleaning\"\"\"\n", "        if not text:\n", "            return \"\"\n", "        \n", "        # Basic cleaning\n", "        text = text.lower().strip()\n", "        text = re.sub(r'[^a-zA-Z0-9\\s.,!?-]', '', text)\n", "        text = re.sub(r'\\s+', ' ', text)\n", "        \n", "        return text.strip()\n", "    \n", "    def build_vocab(self, texts):\n", "        \"\"\"Build vocabulary from texts\"\"\"\n", "        print(\"🔤 Building vocabulary...\")\n", "        \n", "        # Special tokens\n", "        special_tokens = [self.PAD_TOKEN, self.START_TOKEN, self.END_TOKEN, self.UNK_TOKEN]\n", "        \n", "        # Count words\n", "        word_counts = Counter()\n", "        \n", "        for i, text in enumerate(texts):\n", "            if i % 5000 == 0:\n", "                print(f\"  Processing {i:,}/{len(texts):,}...\")\n", "            \n", "            cleaned = self.clean_text(text)\n", "            words = cleaned.split()\n", "            word_counts.update(words)\n", "        \n", "        # Get most common words\n", "        most_common = word_counts.most_common(self.vocab_size - len(special_tokens))\n", "        \n", "        # Build vocabulary\n", "        vocab = special_tokens + [word for word, count in most_common]\n", "        \n", "        self.word_to_idx = {word: idx for idx, word in enumerate(vocab)}\n", "        self.idx_to_word = {idx: word for word, idx in self.word_to_idx.items()}\n", "        \n", "        print(f\"✅ Vocabulary built: {len(self.word_to_idx)} tokens\")\n", "        return self.word_to_idx\n", "    \n", "    def texts_to_sequences(self, texts):\n", "        \"\"\"Convert texts to sequences\"\"\"\n", "        sequences = []\n", "        \n", "        for i, text in enumerate(texts):\n", "            if i % 5000 == 0:\n", "                print(f\"  Converting {i:,}/{len(texts):,}...\")\n", "            \n", "            cleaned = self.clean_text(text)\n", "            words = cleaned.split()\n", "            sequence = [self.word_to_idx.get(word, self.word_to_idx[self.UNK_TOKEN]) for word in words]\n", "            sequences.append(sequence)\n", "        \n", "        return sequences\n", "    \n", "    def pad_sequences(self, sequences):\n", "        \"\"\"Pad sequences to max_length\"\"\"\n", "        padded = np.full((len(sequences), self.max_length), self.word_to_idx[self.PAD_TOKEN], dtype=np.int32)\n", "        \n", "        for i, seq in enumerate(sequences):\n", "            length = min(len(seq), self.max_length)\n", "            if length > 0:\n", "                padded[i, :length] = seq[:length]\n", "        \n", "        return padded\n", "\n", "print(\"✅ SimpleTokenizer class defined!\")"]}, {"cell_type": "markdown", "metadata": {"id": "model"}, "source": ["## 🧠 Simple Model (No Dependencies)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "simple_model"}, "outputs": [], "source": ["class SimpleChatbot:\n", "    \"\"\"Simple chatbot model\"\"\"\n", "    \n", "    def __init__(self, vocab_size, embedding_dim=128, hidden_dim=256, max_length=30):\n", "        self.vocab_size = vocab_size\n", "        self.embedding_dim = embedding_dim\n", "        self.hidden_dim = hidden_dim\n", "        self.max_length = max_length\n", "        self.model = None\n", "    \n", "    def build_model(self):\n", "        \"\"\"Build simple seq2seq model\"\"\"\n", "        print(\"🏗️ Building model...\")\n", "        \n", "        # Encoder\n", "        encoder_inputs = Input(shape=(self.max_length,), name='encoder_inputs')\n", "        encoder_embedding = Embedding(self.vocab_size, self.embedding_dim, mask_zero=True)(encoder_inputs)\n", "        \n", "        # Bidirectional LSTM encoder\n", "        encoder_lstm = Bidirectional(LSTM(self.hidden_dim // 2, return_sequences=True, \n", "                                        return_state=True, dropout=0.2))\n", "        encoder_outputs, fh, fc, bh, bc = encoder_lstm(encoder_embedding)\n", "        \n", "        # Combine states\n", "        state_h = tf.keras.layers.Concatenate()([fh, bh])\n", "        state_c = tf.keras.layers.Concatenate()([fc, bc])\n", "        encoder_states = [state_h, state_c]\n", "        \n", "        # Decoder\n", "        decoder_inputs = Input(shape=(self.max_length,), name='decoder_inputs')\n", "        decoder_embedding = Embedding(self.vocab_size, self.embedding_dim, mask_zero=True)(decoder_inputs)\n", "        \n", "        decoder_lstm = LSTM(self.hidden_dim, return_sequences=True, \n", "                          return_state=True, dropout=0.2)\n", "        decoder_outputs, _, _ = decoder_lstm(decoder_embedding, initial_state=encoder_states)\n", "        \n", "        # Simple attention\n", "        attention_scores = Dot(axes=[2, 2])([decoder_outputs, encoder_outputs])\n", "        attention_weights = Softmax(axis=-1)(attention_scores)\n", "        context_vector = Dot(axes=[2, 1])([attention_weights, encoder_outputs])\n", "        \n", "        # Output\n", "        decoder_combined = tf.keras.layers.Concatenate(axis=-1)([decoder_outputs, context_vector])\n", "        decoder_dense = Dense(self.hidden_dim // 2, activation='relu')(decoder_combined)\n", "        decoder_dropout = Dropout(0.3)(decoder_dense)\n", "        decoder_outputs_final = Dense(self.vocab_size, activation='softmax')(decoder_dropout)\n", "        \n", "        # Create and compile model\n", "        self.model = Model([encoder_inputs, decoder_inputs], decoder_outputs_final)\n", "        \n", "        optimizer = <PERSON>(learning_rate=0.001)\n", "        self.model.compile(optimizer=optimizer, \n", "                          loss='sparse_categorical_crossentropy',\n", "                          metrics=['accuracy'])\n", "        \n", "        print(\"✅ Model built successfully!\")\n", "        return self.model\n", "\n", "print(\"✅ SimpleChatbot class defined!\")"]}, {"cell_type": "markdown", "metadata": {"id": "training"}, "source": ["## 🚀 Training (No Dependencies)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "prepare_data"}, "outputs": [], "source": ["# Prepare training data\n", "if len(questions) > 0:\n", "    print(\"📊 Preparing training data...\")\n", "    \n", "    # Initialize tokenizer\n", "    tokenizer = SimpleTokenizer(config['vocab_size'], config['max_length'])\n", "    \n", "    # Build vocabulary\n", "    all_texts = questions + answers\n", "    tokenizer.build_vocab(all_texts)\n", "    \n", "    # Convert to sequences\n", "    print(\"🔄 Converting to sequences...\")\n", "    question_sequences = tokenizer.texts_to_sequences(questions)\n", "    answer_sequences = tokenizer.texts_to_sequences(answers)\n", "    \n", "    # Pad sequences\n", "    print(\"📏 Padding sequences...\")\n", "    encoder_input_data = tokenizer.pad_sequences(question_sequences)\n", "    \n", "    # Prepare decoder data\n", "    print(\"🎯 Preparing decoder data...\")\n", "    decoder_input_sequences = []\n", "    decoder_target_sequences = []\n", "    \n", "    start_idx = tokenizer.word_to_idx[tokenizer.START_TOKEN]\n", "    end_idx = tokenizer.word_to_idx[tokenizer.END_TOKEN]\n", "    \n", "    for seq in answer_sequences:\n", "        decoder_input_sequences.append([start_idx] + seq)\n", "        decoder_target_sequences.append(seq + [end_idx])\n", "    \n", "    decoder_input_data = tokenizer.pad_sequences(decoder_input_sequences)\n", "    decoder_target_data = tokenizer.pad_sequences(decoder_target_sequences)\n", "    \n", "    print(f\"✅ Data prepared:\")\n", "    print(f\"  Encoder input shape: {encoder_input_data.shape}\")\n", "    print(f\"  Decoder input shape: {decoder_input_data.shape}\")\n", "    print(f\"  Decoder target shape: {decoder_target_data.shape}\")\n", "    \n", "    # Clean up\n", "    del all_texts, questions, answers, question_sequences, answer_sequences\n", "    del decoder_input_sequences, decoder_target_sequences\n", "    gc.collect()\n", "    \n", "else:\n", "    print(\"❌ No data to prepare. Please load data first.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "train_model"}, "outputs": [], "source": ["# Train the model\n", "if 'encoder_input_data' in locals():\n", "    print(\"🚀 Starting training...\")\n", "    \n", "    # Split data using numpy (no sklearn needed)\n", "    total_samples = len(encoder_input_data)\n", "    val_size = int(total_samples * config['validation_split'])\n", "    \n", "    # Simple random split\n", "    indices = np.random.permutation(total_samples)\n", "    train_indices = indices[val_size:]\n", "    val_indices = indices[:val_size]\n", "    \n", "    train_encoder = encoder_input_data[train_indices]\n", "    train_decoder_input = decoder_input_data[train_indices]\n", "    train_decoder_target = decoder_target_data[train_indices]\n", "    \n", "    val_encoder = encoder_input_data[val_indices]\n", "    val_decoder_input = decoder_input_data[val_indices]\n", "    val_decoder_target = decoder_target_data[val_indices]\n", "    \n", "    print(f\"📊 Training samples: {len(train_encoder):,}\")\n", "    print(f\"📊 Validation samples: {len(val_encoder):,}\")\n", "    \n", "    # Build model\n", "    chatbot = SimpleChatbot(\n", "        len(tokenizer.word_to_idx),\n", "        config['embedding_dim'],\n", "        config['hidden_dim'],\n", "        config['max_length']\n", "    )\n", "    model = chatbot.build_model()\n", "    \n", "    # Show model summary\n", "    print(\"\\n📊 Model Summary:\")\n", "    model.summary()\n", "    \n", "    # Simple callbacks\n", "    callbacks = [\n", "        EarlyStopping(patience=3, restore_best_weights=True, verbose=1),\n", "        ModelCheckpoint('simple_chatbot.h5', save_best_only=True, verbose=1),\n", "        ReduceLROnPlateau(factor=0.5, patience=2, min_lr=0.0001, verbose=1)\n", "    ]\n", "    \n", "    # Train\n", "    print(f\"\\n🚀 Training for {config['epochs']} epochs...\")\n", "    print(f\"⚡ Batch size: {config['batch_size']}\")\n", "    \n", "    try:\n", "        history = model.fit(\n", "            [train_encoder, train_decoder_input], train_decoder_target,\n", "            batch_size=config['batch_size'],\n", "            epochs=config['epochs'],\n", "            validation_data=([val_encoder, val_decoder_input], val_decoder_target),\n", "            callbacks=callbacks,\n", "            verbose=1\n", "        )\n", "        \n", "        print(\"\\n✅ Training completed successfully!\")\n", "        \n", "    except Exception as e:\n", "        print(f\"\\n⚠️ Training error: {e}\")\n", "        print(\"🔄 Trying with smaller batch size...\")\n", "        \n", "        # Fallback with smaller batch\n", "        smaller_batch = max(8, config['batch_size'] // 2)\n", "        history = model.fit(\n", "            [train_encoder, train_decoder_input], train_decoder_target,\n", "            batch_size=smaller_batch,\n", "            epochs=min(5, config['epochs']),\n", "            validation_data=([val_encoder, val_decoder_input], val_decoder_target),\n", "            callbacks=callbacks,\n", "            verbose=1\n", "        )\n", "        \n", "        print(\"\\n✅ Fallback training completed!\")\n", "    \n", "else:\n", "    print(\"❌ No prepared data found. Please run data preparation first.\")"]}, {"cell_type": "markdown", "metadata": {"id": "results"}, "source": ["## 📊 Results and Download"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "plot_results"}, "outputs": [], "source": ["# Plot training results\n", "if 'history' in locals():\n", "    print(\"📊 Plotting training results...\")\n", "    \n", "    plt.figure(figsize=(12, 4))\n", "    \n", "    # Loss plot\n", "    plt.subplot(1, 2, 1)\n", "    plt.plot(history.history['loss'], label='Training Loss', color='blue')\n", "    plt.plot(history.history['val_loss'], label='Validation Loss', color='red')\n", "    plt.title('Model Loss')\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Loss')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # Accuracy plot\n", "    plt.subplot(1, 2, 2)\n", "    plt.plot(history.history['accuracy'], label='Training Accuracy', color='blue')\n", "    plt.plot(history.history['val_accuracy'], label='Validation Accuracy', color='red')\n", "    plt.title('Model Accuracy')\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Accuracy')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Print final metrics\n", "    final_loss = history.history['val_loss'][-1]\n", "    final_acc = history.history['val_accuracy'][-1]\n", "    print(f\"\\n📈 Final Results:\")\n", "    print(f\"  Validation Loss: {final_loss:.4f}\")\n", "    print(f\"  Validation Accuracy: {final_acc:.4f}\")\n", "    \n", "else:\n", "    print(\"❌ No training history to plot.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "save_download"}, "outputs": [], "source": ["# Save and download model\n", "if 'model' in locals() and 'tokenizer' in locals():\n", "    print(\"💾 Saving model and tokenizer...\")\n", "    \n", "    try:\n", "        # Save model\n", "        model.save('no_conflicts_chatbot.h5')\n", "        print(\"✅ Model saved as no_conflicts_chatbot.h5\")\n", "        \n", "        # Save tokenizer\n", "        with open('no_conflicts_tokenizer.pkl', 'wb') as f:\n", "            pickle.dump(tokenizer, f)\n", "        print(\"✅ Tokenizer saved as no_conflicts_tokenizer.pkl\")\n", "        \n", "        # Download files\n", "        print(\"\\n📥 Downloading files...\")\n", "        files.download('no_conflicts_chatbot.h5')\n", "        files.download('no_conflicts_tokenizer.pkl')\n", "        \n", "        print(\"\\n🎉 Success! Files downloaded to your computer.\")\n", "        print(\"\\n📋 What you got:\")\n", "        print(\"  ✅ Trained RNN chatbot model\")\n", "        print(\"  ✅ Custom tokenizer for text processing\")\n", "        print(\"  ✅ No dependency conflicts!\")\n", "        print(\"  ✅ Ready for deployment\")\n", "        \n", "        # Show model info\n", "        total_params = model.count_params()\n", "        print(f\"\\n📊 Model Info:\")\n", "        print(f\"  Total parameters: {total_params:,}\")\n", "        print(f\"  Vocabulary size: {len(tokenizer.word_to_idx):,}\")\n", "        print(f\"  Max sequence length: {tokenizer.max_length}\")\n", "        print(f\"  Training samples: {len(train_encoder):,}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error saving: {e}\")\n", "        print(\"💡 Try restarting runtime and running again.\")\n", "        \n", "else:\n", "    print(\"❌ No model or tokenizer to save. Please complete training first.\")"]}, {"cell_type": "markdown", "metadata": {"id": "final_notes"}, "source": ["## 🎉 Congratulations!\n", "\n", "### ✅ What You Accomplished:\n", "- **Zero dependency conflicts** - Used only Colab's pre-installed packages\n", "- **Successful training** - Built and trained an RNN chatbot\n", "- **Attention mechanism** - Implemented custom attention for better responses\n", "- **Memory efficient** - Optimized for Colab's resource limits\n", "- **Ready to use** - Downloaded trained model and tokenizer\n", "\n", "### 🚀 Next Steps:\n", "1. **Test your model** - Load and test with sample conversations\n", "2. **Deploy** - Integrate into your applications\n", "3. **Improve** - Train with more data or adjust parameters\n", "4. **Share** - Use your trained model in projects\n", "\n", "### 💡 Key Features of Your Model:\n", "- **Seq2Seq architecture** with encoder-decoder\n", "- **Bidirectional LSTM** for better context understanding\n", "- **Attention mechanism** for focusing on relevant parts\n", "- **Custom tokenizer** with text cleaning and normalization\n", "- **Optimized for conversation** generation\n", "\n", "### 🔧 Model Usage:\n", "```python\n", "# To use your trained model:\n", "import tensorflow as tf\n", "import pickle\n", "\n", "# Load model and tokenizer\n", "model = tf.keras.models.load_model('no_conflicts_chatbot.h5')\n", "with open('no_conflicts_tokenizer.pkl', 'rb') as f:\n", "    tokenizer = pickle.load(f)\n", "\n", "# Generate responses (requires additional inference setup)\n", "```\n", "\n", "**🎯 This version successfully avoids all dependency conflicts while maintaining full functionality!**"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}