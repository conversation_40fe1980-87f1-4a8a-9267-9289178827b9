{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# 🛡️ Stable RNN Conversational AI - Crash-Resistant Colab Version\n", "\n", "This notebook is optimized to prevent crashes and handle limited resources gracefully.\n", "\n", "## 🛡️ Stability Features:\n", "- **Memory monitoring** and automatic parameter adjustment\n", "- **Crash recovery** with reduced parameters\n", "- **Progressive training** with checkpoints\n", "- **Resource optimization** based on available memory\n", "- **Lightweight model** architecture\n", "\n", "## 📋 Instructions:\n", "1. **Enable GPU**: Runtime → Change runtime type → GPU\n", "2. **Upload data**: Upload `chatbot_data.zip` when prompted\n", "3. **Run all cells**: Execute in order - the system will auto-adjust parameters\n", "4. **Monitor progress**: Watch memory usage and training metrics\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## 📦 Installation and Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install"}, "outputs": [], "source": ["# Install packages with specific versions for stability\n", "!pip install tensorflow==2.13.0\n", "!pip install numpy==1.24.3 pandas matplotlib seaborn\n", "!pip install scikit-learn==1.3.0\n", "!pip install tqdm psutil"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "imports"}, "outputs": [], "source": ["# Import libraries\n", "import os\n", "import json\n", "import zipfile\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from tqdm import tqdm\n", "import logging\n", "import pickle\n", "import re\n", "import gc\n", "import psutil\n", "from typing import List, Dict, Tuple, Optional\n", "from collections import Counter\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# TensorFlow imports\n", "import tensorflow as tf\n", "from tensorflow.keras.models import Model\n", "from tensorflow.keras.layers import (\n", "    Input, LSTM, Dense, Embedding, Dropout, \n", "    LayerNormalization, Bidirectional, Dot, Softmax\n", ")\n", "from tensorflow.keras.optimizers import Adam\n", "from tensorflow.keras.callbacks import EarlyStopping, ModelCheckpoint, ReduceLROnPlateau\n", "from sklearn.model_selection import train_test_split\n", "\n", "# Set up logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n", "\n", "print(\"✅ All libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {"id": "resource_monitor"}, "source": ["## 🔧 Resource Monitoring and Auto-Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "resource_setup"}, "outputs": [], "source": ["class ResourceMonitor:\n", "    \"\"\"Monitor and manage system resources\"\"\"\n", "    \n", "    @staticmethod\n", "    def get_memory_usage():\n", "        \"\"\"Get current memory usage in GB\"\"\"\n", "        memory = psutil.virtual_memory()\n", "        return {\n", "            'total': memory.total / (1024**3),\n", "            'available': memory.available / (1024**3),\n", "            'used': memory.used / (1024**3),\n", "            'percent': memory.percent\n", "        }\n", "    \n", "    @staticmethod\n", "    def get_optimal_config():\n", "        \"\"\"Get optimal configuration based on available resources\"\"\"\n", "        memory = ResourceMonitor.get_memory_usage()\n", "        \n", "        # Auto-adjust based on available memory\n", "        if memory['available'] < 6:  # Less than 6GB available - ULTRA LIGHT\n", "            config = {\n", "                'vocab_size': 5000,\n", "                'max_length': 20,\n", "                'embedding_dim': 64,\n", "                'hidden_dim': 128,\n", "                'epochs': 5,\n", "                'batch_size': 8,\n", "                'validation_split': 0.2,\n", "                'sample_size': 20000\n", "            }\n", "            print(\"🔧 Using ULTRA-LIGHT configuration (< 6GB RAM)\")\n", "        elif memory['available'] < 8:  # 6-8GB available - LIGHT\n", "            config = {\n", "                'vocab_size': 8000,\n", "                'max_length': 25,\n", "                'embedding_dim': 96,\n", "                'hidden_dim': 192,\n", "                'epochs': 8,\n", "                'batch_size': 16,\n", "                'validation_split': 0.2,\n", "                'sample_size': 40000\n", "            }\n", "            print(\"🔧 Using LIGHT configuration (6-8GB RAM)\")\n", "        elif memory['available'] < 10:  # 8-10GB available - MEDIUM\n", "            config = {\n", "                'vocab_size': 12000,\n", "                'max_length': 35,\n", "                'embedding_dim': 128,\n", "                'hidden_dim': 256,\n", "                'epochs': 12,\n", "                'batch_size': 24,\n", "                'validation_split': 0.2,\n", "                'sample_size': 80000\n", "            }\n", "            print(\"🔧 Using MEDIUM configuration (8-10GB RAM)\")\n", "        else:  # More than 10GB available - FULL\n", "            config = {\n", "                'vocab_size': 15000,\n", "                'max_length': 40,\n", "                'embedding_dim': 192,\n", "                'hidden_dim': 384,\n", "                'epochs': 15,\n", "                'batch_size': 32,\n", "                'validation_split': 0.2,\n", "                'sample_size': None\n", "            }\n", "            print(\"🔧 Using FULL configuration (>10GB RAM)\")\n", "        \n", "        return config\n", "    \n", "    @staticmethod\n", "    def cleanup_memory():\n", "        \"\"\"Clean up memory\"\"\"\n", "        gc.collect()\n", "        if 'tf' in globals():\n", "            tf.keras.backend.clear_session()\n", "\n", "def setup_gpu_stable():\n", "    \"\"\"Configure GPU settings for stability\"\"\"\n", "    print(\"🔍 Checking GPU availability...\")\n", "    \n", "    gpus = tf.config.experimental.list_physical_devices('GPU')\n", "    if gpus:\n", "        try:\n", "            for gpu in gpus:\n", "                tf.config.experimental.set_memory_growth(gpu, True)\n", "                # Conservative memory limit to prevent crashes\n", "                tf.config.experimental.set_memory_limit(gpu, 6144)  # 6GB limit\n", "            print(f\"✅ Found {len(gpus)} GPU(s) with conservative memory settings\")\n", "        except RuntimeError as e:\n", "            print(f\"⚠️ GPU setup warning: {e}\")\n", "    else:\n", "        print(\"⚠️ No GPU found, using CPU\")\n", "    \n", "    return len(gpus) > 0\n", "\n", "# Setup and get configuration\n", "has_gpu = setup_gpu_stable()\n", "config = ResourceMonitor.get_optimal_config()\n", "\n", "print(f\"\\n📊 System Resources:\")\n", "memory_info = ResourceMonitor.get_memory_usage()\n", "for key, value in memory_info.items():\n", "    if key != 'percent':\n", "        print(f\"  {key}: {value:.1f} GB\")\n", "    else:\n", "        print(f\"  {key}: {value:.1f}%\")\n", "\n", "print(f\"\\n📊 Auto-Selected Configuration:\")\n", "for key, value in config.items():\n", "    print(f\"  {key}: {value}\")\n", "\n", "print(f\"\\n⚡ Using: {'GPU' if has_gpu else 'CPU'}\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_upload"}, "source": ["## 📁 Data Upload and Extraction"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "upload_data"}, "outputs": [], "source": ["from google.colab import files\n", "\n", "# Upload the zip file\n", "print(\"📁 Please upload your chatbot_data.zip file:\")\n", "uploaded = files.upload()\n", "\n", "# Check if zip file was uploaded\n", "zip_files = [f for f in uploaded.keys() if f.endswith('.zip')]\n", "if zip_files:\n", "    print(f\"✅ Uploaded: {zip_files[0]}\")\n", "else:\n", "    print(\"❌ No zip file uploaded. Please upload chatbot_data.zip\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "extract_data"}, "outputs": [], "source": ["def extract_data_files_stable():\n", "    \"\"\"Extract data files with error handling\"\"\"\n", "    print(\"📁 Extracting data files safely...\")\n", "    \n", "    try:\n", "        zip_files = [f for f in os.listdir('.') if f.endswith('.zip') and 'chatbot' in f.lower()]\n", "        \n", "        if not zip_files:\n", "            print(\"❌ No chatbot data zip file found!\")\n", "            return []\n", "        \n", "        zip_file = zip_files[0]\n", "        print(f\"📦 Found zip file: {zip_file}\")\n", "        \n", "        with zipfile.ZipFile(zip_file, 'r') as zip_ref:\n", "            file_list = zip_ref.namelist()\n", "            json_files = [f for f in file_list if f.endswith('.json')]\n", "            \n", "            print(f\"📊 Extracting {len(json_files)} JSON files...\")\n", "            for file in tqdm(json_files, desc=\"Extracting\"):\n", "                zip_ref.extract(file, '.')\n", "        \n", "        # Verify and show file sizes\n", "        extracted_files = []\n", "        total_size = 0\n", "        for file in json_files:\n", "            if os.path.exists(file):\n", "                size_mb = os.path.getsize(file) / (1024 * 1024)\n", "                total_size += size_mb\n", "                print(f\"✅ {file} ({size_mb:.1f} MB)\")\n", "                extracted_files.append(file)\n", "        \n", "        print(f\"\\n📊 Total data size: {total_size:.1f} MB\")\n", "        \n", "        # Limit files if memory is very low\n", "        memory = ResourceMonitor.get_memory_usage()\n", "        if memory['available'] < 6 and len(extracted_files) > 3:\n", "            print(\"⚠️ Limited memory detected - using only 3 largest files\")\n", "            # Sort by file size and take the 3 largest\n", "            file_sizes = [(f, os.path.getsize(f)) for f in extracted_files]\n", "            file_sizes.sort(key=lambda x: x[1], reverse=True)\n", "            extracted_files = [f[0] for f in file_sizes[:3]]\n", "        \n", "        return extracted_files\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error extracting files: {e}\")\n", "        return []\n", "\n", "# Extract data files\n", "data_files = extract_data_files_stable()\n", "print(f\"\\n📊 Ready to use {len(data_files)} data files\")"]}, {"cell_type": "markdown", "metadata": {"id": "model_classes"}, "source": ["## 🧠 Lightweight Model and Training Classes"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "lightweight_model"}, "outputs": [], "source": ["class LightweightTokenizer:\n", "    \"\"\"Memory-efficient tokenizer\"\"\"\n", "    \n", "    def __init__(self, vocab_size: int = 5000, max_length: int = 20):\n", "        self.vocab_size = vocab_size\n", "        self.max_length = max_length\n", "        self.word_to_idx = {}\n", "        self.idx_to_word = {}\n", "        \n", "        self.PAD_TOKEN = '<PAD>'\n", "        self.START_TOKEN = '<START>'\n", "        self.END_TOKEN = '<END>'\n", "        self.UNK_TOKEN = '<UNK>'\n", "    \n", "    def clean_text(self, text: str) -> str:\n", "        if not text:\n", "            return \"\"\n", "        text = text.lower().strip()\n", "        text = re.sub(r'\\s+', ' ', text)\n", "        text = re.sub(r'[^\\w\\s.,!?-]', '', text)\n", "        return text.strip()\n", "    \n", "    def build_vocab(self, texts: List[str]):\n", "        print(\"🔤 Building vocabulary...\")\n", "        \n", "        special_tokens = [self.PAD_TOKEN, self.START_TOKEN, self.END_TOKEN, self.UNK_TOKEN]\n", "        word_freq = Counter()\n", "        \n", "        # Process in smaller batches\n", "        batch_size = 1000\n", "        for i in tqdm(range(0, len(texts), batch_size), desc=\"Processing texts\"):\n", "            batch = texts[i:i + batch_size]\n", "            for text in batch:\n", "                cleaned_text = self.clean_text(text)\n", "                words = cleaned_text.split()\n", "                word_freq.update(words)\n", "            \n", "            if i % 10000 == 0:\n", "                gc.collect()\n", "        \n", "        most_common = word_freq.most_common(self.vocab_size - len(special_tokens))\n", "        vocab = special_tokens + [word for word, _ in most_common]\n", "        \n", "        self.word_to_idx = {word: idx for idx, word in enumerate(vocab)}\n", "        self.idx_to_word = {idx: word for word, idx in self.word_to_idx.items()}\n", "        \n", "        print(f\"✅ Vocabulary built with {len(self.word_to_idx)} tokens\")\n", "        return self.word_to_idx\n", "    \n", "    def texts_to_sequences(self, texts: List[str]) -> List[List[int]]:\n", "        sequences = []\n", "        for text in tqdm(texts, desc=\"Converting to sequences\"):\n", "            cleaned_text = self.clean_text(text)\n", "            words = cleaned_text.split()\n", "            sequence = [self.word_to_idx.get(word, self.word_to_idx[self.UNK_TOKEN]) for word in words]\n", "            sequences.append(sequence)\n", "        return sequences\n", "    \n", "    def pad_sequences(self, sequences: List[List[int]]) -> np.ndarray:\n", "        padded = np.full((len(sequences), self.max_length), self.word_to_idx[self.PAD_TOKEN], dtype=np.int32)\n", "        for i, seq in enumerate(sequences):\n", "            length = min(len(seq), self.max_length)\n", "            if length > 0:\n", "                padded[i, :length] = seq[:length]\n", "        return padded\n", "\n", "class CrashResistantChatbot:\n", "    \"\"\"Ultra-lightweight chatbot model\"\"\"\n", "    \n", "    def __init__(self, vocab_size: int, embedding_dim: int = 64, \n", "                 hidden_dim: int = 128, max_length: int = 20):\n", "        self.vocab_size = vocab_size\n", "        self.embedding_dim = embedding_dim\n", "        self.hidden_dim = hidden_dim\n", "        self.max_length = max_length\n", "        self.model = None\n", "    \n", "    def build_model(self):\n", "        print(\"🏗️ Building crash-resistant model...\")\n", "        \n", "        # Encoder\n", "        encoder_inputs = Input(shape=(self.max_length,), name='encoder_inputs')\n", "        encoder_embedding = Embedding(self.vocab_size, self.embedding_dim, \n", "                                    mask_zero=True)(encoder_inputs)\n", "        \n", "        # Simple bidirectional LSTM\n", "        encoder_lstm = Bidirectional(LSTM(self.hidden_dim // 2, return_sequences=True, \n", "                                        return_state=True, dropout=0.1))\n", "        encoder_outputs, fh, fc, bh, bc = encoder_lstm(encoder_embedding)\n", "        \n", "        state_h = tf.keras.layers.Concatenate()([fh, bh])\n", "        state_c = tf.keras.layers.Concatenate()([fc, bc])\n", "        encoder_states = [state_h, state_c]\n", "        \n", "        # Decoder\n", "        decoder_inputs = Input(shape=(self.max_length,), name='decoder_inputs')\n", "        decoder_embedding = Embedding(self.vocab_size, self.embedding_dim, \n", "                                    mask_zero=True)(decoder_inputs)\n", "        \n", "        decoder_lstm = LSTM(self.hidden_dim, return_sequences=True, \n", "                          return_state=True, dropout=0.1)\n", "        decoder_outputs, _, _ = decoder_lstm(decoder_embedding, initial_state=encoder_states)\n", "        \n", "        # Simplified attention\n", "        attention_scores = Dot(axes=[2, 2])([decoder_outputs, encoder_outputs])\n", "        attention_weights = Softmax(axis=-1)(attention_scores)\n", "        context_vector = Dot(axes=[2, 1])([attention_weights, encoder_outputs])\n", "        \n", "        # Output\n", "        decoder_combined = tf.keras.layers.Concatenate(axis=-1)([decoder_outputs, context_vector])\n", "        decoder_dense = Dense(self.hidden_dim // 2, activation='relu')(decoder_combined)\n", "        decoder_dropout = Dropout(0.2)(decoder_dense)\n", "        decoder_outputs_final = Dense(self.vocab_size, activation='softmax', dtype='float32')(decoder_dropout)\n", "        \n", "        self.model = Model([encoder_inputs, decoder_inputs], decoder_outputs_final)\n", "        \n", "        # Conservative optimizer settings\n", "        optimizer = <PERSON>(learning_rate=0.001, clipnorm=1.0)\n", "        self.model.compile(optimizer=optimizer, \n", "                          loss='sparse_categorical_crossentropy',\n", "                          metrics=['accuracy'])\n", "        \n", "        print(\"✅ Crash-resistant model built!\")\n", "        return self.model\n", "\n", "print(\"✅ Lightweight classes defined!\")"]}, {"cell_type": "markdown", "metadata": {"id": "training_section"}, "source": ["## 🚀 Safe Training Execution"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_data_safe"}, "outputs": [], "source": ["def load_data_safely(data_files, config):\n", "    \"\"\"Load data with memory management\"\"\"\n", "    print(\"📚 Loading data safely...\")\n", "    \n", "    all_questions = []\n", "    all_answers = []\n", "    \n", "    for file_path in tqdm(data_files, desc=\"Loading files\"):\n", "        try:\n", "            with open(file_path, 'r', encoding='utf-8') as f:\n", "                data = json.load(f)\n", "            \n", "            file_questions = []\n", "            file_answers = []\n", "            \n", "            # Process in smaller chunks\n", "            for item in data[:10000]:  # Limit per file for safety\n", "                question = item.get('ask', '').strip()\n", "                answers = item.get('ans', [])\n", "                \n", "                if question and answers:\n", "                    if isinstance(answers, list):\n", "                        # Take only first answer to save memory\n", "                        if answers[0].strip():\n", "                            file_questions.append(question)\n", "                            file_answers.append(answers[0].strip())\n", "                    else:\n", "                        file_questions.append(question)\n", "                        file_answers.append(str(answers).strip())\n", "                \n", "                # Check sample size limit\n", "                if (config.get('sample_size') and \n", "                    len(all_questions) + len(file_questions) >= config['sample_size']):\n", "                    break\n", "            \n", "            all_questions.extend(file_questions)\n", "            all_answers.extend(file_answers)\n", "            \n", "            print(f\"✅ Loaded {len(file_questions):,} from {file_path}\")\n", "            \n", "            # Clean up\n", "            del data, file_questions, file_answers\n", "            gc.collect()\n", "            \n", "            # Check if we've reached the limit\n", "            if (config.get('sample_size') and \n", "                len(all_questions) >= config['sample_size']):\n", "                print(f\"📊 Reached sample limit of {config['sample_size']:,}\")\n", "                break\n", "                \n", "        except Exception as e:\n", "            print(f\"❌ Error loading {file_path}: {e}\")\n", "            continue\n", "    \n", "    print(f\"📊 Total loaded: {len(all_questions):,} conversations\")\n", "    return all_questions, all_answers\n", "\n", "# Load data if files are available\n", "if data_files:\n", "    questions, answers = load_data_safely(data_files, config)\n", "    print(f\"✅ Data loading completed: {len(questions):,} samples\")\n", "else:\n", "    print(\"❌ No data files available. Please upload and extract data first!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "train_safe"}, "outputs": [], "source": ["# Safe training execution\n", "if 'questions' in locals() and 'answers' in locals():\n", "    print(\"🚀 Starting safe training...\")\n", "    \n", "    try:\n", "        # Initialize tokenizer\n", "        tokenizer = LightweightTokenizer(config['vocab_size'], config['max_length'])\n", "        \n", "        # Build vocabulary\n", "        all_texts = questions + answers\n", "        tokenizer.build_vocab(all_texts)\n", "        \n", "        # Prepare data\n", "        print(\"📊 Preparing training data...\")\n", "        question_sequences = tokenizer.texts_to_sequences(questions)\n", "        answer_sequences = tokenizer.texts_to_sequences(answers)\n", "        \n", "        encoder_input_data = tokenizer.pad_sequences(question_sequences)\n", "        \n", "        # Prepare decoder data\n", "        decoder_input_sequences = []\n", "        decoder_target_sequences = []\n", "        \n", "        start_idx = tokenizer.word_to_idx[tokenizer.START_TOKEN]\n", "        end_idx = tokenizer.word_to_idx[tokenizer.END_TOKEN]\n", "        \n", "        for seq in answer_sequences:\n", "            decoder_input_sequences.append([start_idx] + seq)\n", "            decoder_target_sequences.append(seq + [end_idx])\n", "        \n", "        decoder_input_data = tokenizer.pad_sequences(decoder_input_sequences)\n", "        decoder_target_data = tokenizer.pad_sequences(decoder_target_sequences)\n", "        \n", "        # Clean up\n", "        del all_texts, questions, answers, question_sequences, answer_sequences\n", "        del decoder_input_sequences, decoder_target_sequences\n", "        ResourceMonitor.cleanup_memory()\n", "        \n", "        # Split data\n", "        indices = np.arange(len(encoder_input_data))\n", "        train_indices, val_indices = train_test_split(\n", "            indices, test_size=config['validation_split'], random_state=42\n", "        )\n", "        \n", "        train_encoder = encoder_input_data[train_indices]\n", "        train_decoder_input = decoder_input_data[train_indices]\n", "        train_decoder_target = decoder_target_data[train_indices]\n", "        \n", "        val_encoder = encoder_input_data[val_indices]\n", "        val_decoder_input = decoder_input_data[val_indices]\n", "        val_decoder_target = decoder_target_data[val_indices]\n", "        \n", "        print(f\"📊 Training samples: {len(train_encoder):,}\")\n", "        print(f\"📊 Validation samples: {len(val_encoder):,}\")\n", "        \n", "        # Build model\n", "        chatbot = CrashResistantChatbot(\n", "            len(tokenizer.word_to_idx),\n", "            config['embedding_dim'],\n", "            config['hidden_dim'],\n", "            config['max_length']\n", "        )\n", "        model = chatbot.build_model()\n", "        \n", "        print(\"\\n📊 Model Summary:\")\n", "        model.summary()\n", "        \n", "        # Conservative callbacks\n", "        callbacks = [\n", "            EarlyStopping(patience=2, restore_best_weights=True, verbose=1),\n", "            ModelCheckpoint('crash_resistant_model.h5', save_best_only=True, verbose=1),\n", "            ReduceLROnPlateau(factor=0.8, patience=1, min_lr=0.0001, verbose=1)\n", "        ]\n", "        \n", "        # Train with error handling\n", "        print(f\"\\n🚀 Starting training with {config['epochs']} epochs...\")\n", "        print(f\"⚡ Batch size: {config['batch_size']}\")\n", "        \n", "        try:\n", "            history = model.fit(\n", "                [train_encoder, train_decoder_input], train_decoder_target,\n", "                batch_size=config['batch_size'],\n", "                epochs=config['epochs'],\n", "                validation_data=([val_encoder, val_decoder_input], val_decoder_target),\n", "                callbacks=callbacks,\n", "                verbose=1\n", "            )\n", "            \n", "            print(\"\\n✅ Training completed successfully!\")\n", "            \n", "        except Exception as e:\n", "            print(f\"\\n⚠️ Training error: {e}\")\n", "            print(\"🔄 Attempting recovery with smaller batch size...\")\n", "            \n", "            # Recovery attempt\n", "            smaller_batch = max(4, config['batch_size'] // 2)\n", "            history = model.fit(\n", "                [train_encoder, train_decoder_input], train_decoder_target,\n", "                batch_size=smaller_batch,\n", "                epochs=min(3, config['epochs']),\n", "                validation_data=([val_encoder, val_decoder_input], val_decoder_target),\n", "                callbacks=callbacks,\n", "                verbose=1\n", "            )\n", "            \n", "            print(\"\\n✅ Recovery training completed!\")\n", "        \n", "        # Plot results\n", "        plt.figure(figsize=(12, 4))\n", "        \n", "        plt.subplot(1, 2, 1)\n", "        plt.plot(history.history['loss'], label='Training Loss')\n", "        plt.plot(history.history['val_loss'], label='Validation Loss')\n", "        plt.title('Model Loss')\n", "        plt.xlabel('Epoch')\n", "        plt.ylabel('Loss')\n", "        plt.legend()\n", "        plt.grid(True)\n", "        \n", "        plt.subplot(1, 2, 2)\n", "        plt.plot(history.history['accuracy'], label='Training Accuracy')\n", "        plt.plot(history.history['val_accuracy'], label='Validation Accuracy')\n", "        plt.title('Model Accuracy')\n", "        plt.xlabel('Epoch')\n", "        plt.ylabel('Accuracy')\n", "        plt.legend()\n", "        plt.grid(True)\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # Save model\n", "        model.save('stable_colab_chatbot.h5')\n", "        with open('stable_colab_tokenizer.pkl', 'wb') as f:\n", "            pickle.dump(tokenizer, f)\n", "        \n", "        print(\"\\n💾 Model and tokenizer saved!\")\n", "        print(\"📁 Files: stable_colab_chatbot.h5, stable_colab_tokenizer.pkl\")\n", "        \n", "        # Download files\n", "        files.download('stable_colab_chatbot.h5')\n", "        files.download('stable_colab_tokenizer.pkl')\n", "        \n", "        print(\"\\n🎉 Training completed successfully!\")\n", "        print(\"📥 Files downloaded to your computer.\")\n", "        \n", "    except Exception as e:\n", "        print(f\"\\n❌ Critical error: {e}\")\n", "        print(\"💡 Try restarting runtime and using even smaller parameters.\")\n", "        import traceback\n", "        traceback.print_exc()\n", "        \n", "else:\n", "    print(\"❌ No data loaded. Please run the data loading cell first!\")"]}, {"cell_type": "markdown", "metadata": {"id": "final_notes"}, "source": ["## 📝 Final Notes and Troubleshooting\n", "\n", "### 🛡️ This Stable Version Features:\n", "- **Auto-configuration** based on available memory\n", "- **Crash recovery** with reduced parameters\n", "- **Memory monitoring** throughout training\n", "- **Conservative settings** to prevent OOM errors\n", "- **Progressive fallbacks** if training fails\n", "\n", "### 🔧 If You Still Experience Issues:\n", "1. **Restart Runtime**: Runtime → Restart runtime\n", "2. **Use Smaller Data**: Upload fewer/smaller JSON files\n", "3. **Manual Override**: Modify config values to be even smaller\n", "4. **Check Memory**: Monitor the memory usage indicators\n", "\n", "### 💡 Manual Configuration Override:\n", "```python\n", "# If auto-config is too aggressive, manually set:\n", "config = {\n", "    'vocab_size': 3000,      # Very small vocabulary\n", "    'max_length': 15,        # Very short sequences\n", "    'embedding_dim': 32,     # Tiny embeddings\n", "    'hidden_dim': 64,        # Small hidden size\n", "    'epochs': 3,             # Few epochs\n", "    'batch_size': 4,         # Tiny batches\n", "    'sample_size': 10000     # Very limited data\n", "}\n", "```\n", "\n", "### 🎯 Success Indicators:\n", "- ✅ Model builds without errors\n", "- ✅ Training progresses through epochs\n", "- ✅ Loss decreases over time\n", "- ✅ Files save and download successfully\n", "\n", "**🎉 This version is designed to work even on limited resources!**"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}